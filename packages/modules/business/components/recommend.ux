<script>
import adsdk from '../lib/adsdk'
import { showDialog } from '@system.prompt'
import config from '../lib/config'
import globalData from '@quickapp/utils/lib/globalData'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data: {
    adList: [],
    refreshTag: 0,
    bgStyle: '',
    showMarketDialog: false,
    showService: false,
    showMenubarBg: false,
  },
  onInit() {
    this.$on('onPageBack', this.onPageBack)
    this.$on('onPageRefresh', this.showAd)
    this.$on('downloadclick', this.updateAd)
    this.showMenubarBg = adsdk.provider() === 'honor'
  },
  onReady() {
    this.bgStyle = `background-image: url('http://fishing-h5.springdance.cn/images/cover/1.webp');`
    adsdk.onAdPrepared(() => {
      this.showAd()
      this.showService = config.getAdConfig()['show_complaint']
      let timeout = config.getAdConfig()['native_refresh']
      if (timeout >= 200) {
        setInterval(() => {
          try {
            if (!this.$visible) return
            let isAllShow = this.adList.every(item => item.showTime > 0)
            if (!isAllShow || this.adList.length <= 0) return
            if (adsdk.adCacheList.length > 0) {
              let item = this.adList.reduce((smallest, current) => {
                return current.showTime < smallest.showTime ? current : smallest
              })
              item.outdated = true
              this.$broadcast('updateAd')
            }
          } catch (e) {}
        }, timeout)
      }
    })
  },

  updateBackground() {
    if (globalData.recommendIndex >= 4 || !globalData.recommendIndex) {
      globalData.recommendIndex = 1
    } else {
      globalData.recommendIndex++
    }
    this.bgStyle = `background-image: url('http://fishing-h5.springdance.cn/images/cover/${globalData.recommendIndex}.webp');`
  },
  showAd() {
    this.updateBackground()
    this.adList.forEach(item => {
      item.outdated = true
    })
    this.$broadcast('updateAd')
    setTimeout(() => {
      this.filterAd()
    })
  },
  updateAd() {
    this.showMarketDialog = false
    const refreshOnlyClick = config.getAdConfig()['refresh_only_click']
    if (refreshOnlyClick) {
      this.updateBackground()
      this.$broadcast('updateAd')
    } else {
      this.showAd()
    }
  },
  onAdBgClick() {
    this.showMarketDialog = false
    this.updateBackground()
  },
  filterAd() {
    let adList = this.adList
    if (adList.length === 0) {
      let num = config.getAdConfig()['ad_num'] || 4
      let adParallelNum = config.getAdConfig()['ad_parallel_num'] || 3
      adList = adsdk.createAdRequest(num).map((item, index) => {
        item.parallel = index < adParallelNum
        return item
      })
    }
    let width =
      globalData.deviceInfo.windowWidth || globalData.deviceInfo.screenWidth
    let height =
      globalData.deviceInfo.windowHeight || globalData.deviceInfo.screenHeight
    let screenHeight = height * (750 / width)
    let successSize = adList.filter(item => item.success).length
    let lastSuccessIndex = adList.map(item => item.success).lastIndexOf(true)
    if (adsdk.provider() === 'huawei') {
      let maxAdCount = Math.trunc(screenHeight / 110)
      let adCountPerItem =
        successSize > 0 ? Math.trunc(maxAdCount / successSize) : 0
      let perAdHeight = Math.trunc(screenHeight / maxAdCount)

      this.adList = [
        ...adList.map((item, index, arr) => {
          if (!item.success) {
            item.style = 'height: 0;'
          } else if (successSize > 1 && index === lastSuccessIndex) {
            item.height =
              screenHeight - adCountPerItem * perAdHeight * (successSize - 1)
            item.style = `height: ${item.height}px;`
          } else if (successSize > 1) {
            item.height = adCountPerItem * perAdHeight
            item.style = `height: ${item.height}px;`
          } else {
            item.height = screenHeight
            item.style = `height: ${item.height}px;`
          }
          return item
        }),
      ]
    } else {
      this.adList = [
        ...adList.map((item, index, arr) => {
          if (!item.success) {
            item.style = 'height: 0;'
          } else if (successSize > 1 && index === lastSuccessIndex) {
            item.height = 560
            item.style = `height: ${item.height}px;`
          } else if (successSize > 1) {
            item.height = (screenHeight - 560) / (successSize - 1)
            item.style = `height: ${item.height}px;`
          } else {
            item.height = screenHeight
            item.style = `height: ${item.height}px;`
          }
          return item
        }),
      ]
    }
    //for huawei adb update
    this.$broadcast('updateAd')
  },
  onAdClick() {
    this.showMarketDialog = false
    this.$emit('adclick')
  },
  onAdLoad() {
    this.filterAd()
    setTimeout(() => {
      const showMarketConfig = config.getAdConfig()['show_market_dialog']
      if (showMarketConfig) {
        this.showMarketDialog = true
      }
    }, 800)
  },
  onAdError() {
    this.filterAd()
  },
  onPageBack() {
    this.bgStyle = `background-image: url('http://fishing-h5.springdance.cn/images/cover/5.webp?v=1');`
    if (debug) {
      showDialog({
        message: adsdk.logs.join('\n'),
      })
    }
  },
}
</script>
<import
  name="ad-block"
  src="@quickapp/business/components/ad-block.ux"
></import>

<import name="service" src="@quickapp/mc-ui/components/service.ux"></import>
<template>
  <div class="wrapper" style="{{bgStyle}}">
    <image
      class="menubar-bg"
      src="http://fishing-h5.springdance.cn/images/menubar-bg.webp"
      alt="blank"
      if="showMenubarBg"
    ></image>
    <image
      class="market-dialog"
      src="http://fishing-h5.springdance.cn/images/market_dialog.webp"
      alt="blank"
      if="showMarketDialog"
    ></image>
    <div class="ad-root" @click="onAdBgClick">
      <div
        class="item"
        for="{{adList}}"
        style="{{$item.style}}"
        tid="requestId"
      >
        <ad-block
          reqid="{{$item.requestId}}"
          parallel="{{$item.parallel}}"
          @adclick="onAdClick"
          @load="onAdLoad"
          @error="onAdError"
        ></ad-block>
      </div>
    </div>
    <service if="showService" show-service="{{showService}}"></service>
  </div>
</template>

<style scoped lang="less">
@keyframes translateAnim {
  from {
    transform: translate(0, 435px);
  }
  to {
    transform: translate(0, 0);
  }
}
.menubar-bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 750px;
  height: 196px;
}
.wrapper {
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-size: cover;
  background-position: center bottom;
  position: relative;

  .market-dialog {
    position: absolute;
    left: 35px;
    width: 680px;
    height: 352px;
    bottom: 84px;
    animation-name: translateAnim;
    animation-duration: 0.5s;
    animation-timing-function: ease;
    animation-fill-mode: forwards;
  }

  .ad-root {
    width: 100%;
    flex: 1;
    flex-direction: column;

    .item {
      width: 100%;
    }
  }
}
</style>
