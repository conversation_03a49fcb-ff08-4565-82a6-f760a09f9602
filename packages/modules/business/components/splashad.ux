<script>
import adsdk from '../lib/adsdk'
import config from '../lib/config'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data: {
    adRequest: null,
    showSplashAd: false,
  },
  onReady() {
    adsdk.onAdPrepared(() => {
      let showSplash = config.getAdConfig()['show_splash']
      if (showSplash) {
        this.showAd()
      } else {
        this.$emit('close')
      }
    })
  },
  showAd() {
    this.showSplashAd = true
    this.adRequest = adsdk.createAdRequest(1)[0]
  },
  updateAd() {},
  onAdClick() {
    this.$emit('adclick')
  },
  onAdLoad() {},
  onSkip() {
    this.$broadcast('closeAd')
    this.$emit('close')
  },
  onPageBack() {},
}
</script>
<import
  name="ad-block"
  src="@quickapp/business/components/ad-block.ux"
></import>
<template>
  <div class="wrapper">
    <div class="ad-root" if="showSplashAd">
      <ad-block
        id="ad0"
        reqid="{{adRequest.requestId}}"
        parallel="{{true}}"
        show-splash-ad="{{true}}"
        @adclick="onAdClick"
        @load="onAdLoad"
        @close="onSkip"
      ></ad-block>
    </div>
  </div>
</template>

<style scoped lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  flex-direction: column;
  position: relative;

  .ad-root {
    width: 100%;
    height: 100%;
    background-image: url('http://fishing-h5.springdance.cn/images/bg-open.webp');
    background-size: cover;
    flex-direction: column;
  }
}
</style>
