<template>
  <div>
    <block if="showAd">
      <div class="ad-root" @touchstart="adbboxTouch">
        <div class="container" @click="reportNativeClick">
          <div class="debug" if="{{debugText}}">
            <text>{{ debugText }}</text>
          </div>
          <div class="adbbox" if="showAdbBox">
            <div class="btn-ct" for="{{adbList}}">
              <div class="{{showAdBtn ? 'btn-sc' : 'btn-sc-opacity'}}">
                <ad-button
                  class="adbtn"
                  onclick="startButton()"
                  valuetype="0"
                  adunitid="{{adObj.adUnitId}}"
                  adid="{{adData.adId}}"
                ></ad-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </block>
  </div>
</template>
<style lang="less">
@keyframes opacityAnim {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.ad-root {
  width: 100%;
  height: 100%;
  //transform: scale(1.4);
}
.container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  position: relative;
  //transform: scale(0.7);

  .debug {
    height: 56px;
    border-radius: 28px;
    align-items: center;
    justify-content: center;
    background-color: #2d40e9;
    text {
      font-size: 28px;
      color: white;
      font-weight: bold;
    }
  }

  .adbbox {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    flex-direction: column;
    justify-content: flex-end;

    .btn-ct {
      width: 100%;
      height: 100%;
      align-items: center;
      justify-content: center;

      .btn-sc {
        position: absolute;
        width: 750px;
        height: 100px;
        opacity: 1;
      }

      .btn-sc-opacity {
        position: absolute;
        width: 750px;
        height: 100px;
        transform: scale(60);
        transform-origin: 1px 1px;
        flex-direction: column;
      }

      .adbtn {
        width: 750px;
        height: 100px;
        background-color: #909090;
        font-size: 20px;
      }
    }
  }
}
</style>
<script>
import file from '@system.file'
import adsdk from '../../lib/adsdk'
import { trackEvent } from '../../lib'
import globalData from '@quickapp/utils/lib/globalData'
import config from '../../lib/config'
const debug = process.env.NODE_ENV !== 'production'

export default {
  data: {
    adObj: {},
    adData: {},
    showAd: false,
    showAdbBox: false,
    showAdBtn: false,
    adbList: [],
    showDebug: debug,
    debugText: null,
  },
  props: {
    reqid: {
      type: String,
    },
  },
  onInit() {
    this.$on('updateAd', this.updateAd)
  },
  onReady(options) {
    this.showNativeAd()
  },
  onShow(options) {},
  isDownloadAd(creativeType) {
    let downloadTypes = [103, 106, 107, 108, 101, 102, 110]
    return downloadTypes.includes(creativeType)
  },
  showNativeAd() {
    adsdk.log(`huawei showNativeAd ${this.reqid}`)
    if (this.reqid) {
      this.adObj = adsdk.getAdRequest(this.reqid)?.adObj
    }
    this.adData = this.adObj?.adData
    if (this.adData) {
      if (!this.$valid || !this.$visible) {
        adsdk.adCacheList.push(this.adObj)
        return
      }
      this.$emit('load')
      let adBtnPercent = config.getAdConfig()['ad_btn_percent'] || 0
      this.showAdbBox = Math.random() * 100 <= adBtnPercent
      this.showAd = true
      if (this.showDebug) {
        this.debugText = this.adData.title + ' ' + this.adObj.adUnitId
      }
      this.adShow()
    }
  },
  updateAd() {
    let adConf = adsdk.getAdRequest(this.reqid)
    let adbCount = Math.trunc(adConf?.height / 110)
    if (adbCount > 0) {
      this.adbList = [...Array(adbCount)]
    } else {
      this.adbList = []
    }
  },
  adShow() {
    this.adObj.reportNativeShow()
    this.$emit('show')
    adsdk.log('adShow 信息流广告展示成功：', this.adData, this.adObj.ecpm)
    if (this.showDebug) {
      this.debugText = '曝光: ' + this.adData.title + ' ' + this.adObj.adUnitId
    }
    adsdk.onAdShow()
    trackEvent({
      category: 'advertise',
      action: 'exposure',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
        ecpm: this.adObj.ecpm,
      },
    })
  },
  reportNativeClick() {
    this.adObj?.reportNativeClick()
    adsdk.log('ad huawei click')
    this.$emit('adclick')
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
      },
    })
  },
  adbboxTouch(e) {
    this.showAdBtn = true
    setTimeout(() => {
      this.showAdBtn = false
    }, 500)
  },
  startButton(event) {
    adsdk.log('startButton event: ' + event.resultCode)
    if (event.resultCode < 0) {
      this.showAdbBox = false
      return
    }
    event.stopPropagation()
    if (
      this.adData.clickBtnTxt.includes('安装') ||
      this.adData.clickBtnTxt.includes('下载')
    ) {
      adsdk.log('ad huawei ondownloadclick')
      this.$dispatch('downloadclick')
    }
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
      },
    })
  },
  onDestroy() {
    this.adObj?.destroy()
  },
}
</script>
