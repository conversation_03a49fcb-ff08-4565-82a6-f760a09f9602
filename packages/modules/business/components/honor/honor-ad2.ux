<script>
import { trackEvent } from '../../lib'
import file from '@system.file'
import adsdk from '../../lib/adsdk'
import globalData from '@quickapp/utils/lib/globalData'
import config from '../../lib/config'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data() {
    return {
      adObj: {},
      adData: {},
      showAd: false,
      showAdbBox: false,
      showAdBtn: false,
      adbList: [],
      showDebug: debug,
      debugText: null,
    }
  },
  props: {
    reqid: {
      type: String,
    },
  },
  onInit() {
    this.$on('updateAd', this.updateAd)
  },
  onReady() {
    this.loadAd()
  },
  async loadAd() {
    if (this.reqid) {
      let adConf = adsdk.getAdRequest(this.reqid)
      this.adObj = adConf?.adObj
    }
    this.adData = this.adObj?.adData
    if (this.adData) {
      if (!this.$valid || !this.$visible) {
        adsdk.adCacheList.push(this.adObj)
        return
      }
      this.$emit('load')
      let adBtnPercent = config.getAdConfig()['ad_btn_percent'] || 100
      this.showAdbBox = Math.random() * 100 <= adBtnPercent
      this.showAd = true
      if (this.showDebug) {
        this.debugText = this.adData.title + ' ' + this.adObj.adUnitId
      }
    }
  },
  updateAd() {},
  adShow() {
    this.$emit('show')
    adsdk.log('adShow 信息流广告展示成功：', this.adData, this.adObj.ecpm)
    if (this.showDebug) {
      this.debugText = '曝光: ' + this.adData.title + ' ' + this.adObj.adUnitId
    }
    adsdk.onAdShow()
    trackEvent({
      category: 'advertise',
      action: 'exposure',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
        ecpm: this.adObj.ecpm,
      },
    })
  },
  adClick() {
    adsdk.log('ad honor click')
    this.$emit('adclick')
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
      },
    })
  },
  ondownloadclick() {
    if (this.adData.interactionType === 0) {
      adsdk.log('ad honor ondownloadclick')
      this.$dispatch('downloadclick')
    } else {
      adsdk.log('ad honor click')
      this.$emit('adclick')
    }
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
      },
    })
  },
  ondownloadstatuschange() {},
}
</script>

<template>
  <div>
    <block if="showAd">
      <picture-ad-root
        adid="{{adData.adId}}"
        class="ad-root"
        close_flag_gravity="top_end"
        text_essential_flag_gravity="bottom_start"
        click_essential_flag_gravity="bottom_end"
        onadshow="adShow"
      >
        <div class="container">
          <picture-ad-click-callback-area
            adid="{{adData.adId}}"
            onareaclick="adClick"
            class="click-wrapper"
          >
            <div class="debug" if="{{debugText}}">
              <text>{{ debugText }}</text>
            </div>
          </picture-ad-click-callback-area>
          <div class="drawerbox" if="showAdbBox">
            <drawer autoplay="false" indicator="false" loop="false">
              <ad-button
                adid="{{adData.adid}}"
                if="{{true}}"
                height="100px"
                width="750px"
                font-size="20px"
                onbtnclick="ondownloadclick"
                ondownloadstatuschange="ondownloadstatuschange"
              ></ad-button>
            </drawer>
          </div>
        </div>
      </picture-ad-root>
    </block>
  </div>
</template>

<style lang="less">
@keyframes opacityAnim {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.ad-root {
  width: 100%;
  height: 100%;
  transform: scale(1.4);
}
.container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  position: relative;
  transform: scale(0.72);

  .click-wrapper {
    width: 100%;
    height: 100%;

    .debug {
      height: 56px;
      border-radius: 28px;
      align-items: center;
      justify-content: center;
      background-color: #2d40e9;
      text {
        font-size: 28px;
        color: white;
        font-weight: bold;
      }
    }
  }

  .adbbox {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    flex-direction: column;
    justify-content: flex-end;

    .btn-ct {
      width: 100%;
      height: 100%;
      align-items: center;
      justify-content: center;

      .btn-sc {
        position: absolute;
        width: 750px;
        height: 100px;
        opacity: 1;
      }

      .btn-sc-opacity {
        position: absolute;
        width: 750px;
        height: 100px;
        flex-direction: column;
        animation-name: opacityAnim;
        animation-duration: 0s;
        animation-iteration-count: infinite;
      }
    }
  }

  .drawerbox {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
</style>
