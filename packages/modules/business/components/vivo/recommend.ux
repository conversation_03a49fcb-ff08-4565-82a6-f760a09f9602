<script>
import adsdk from '../../lib/adsdk'
import { showDialog } from '@system.prompt'
import config from '../../lib/config'
import globalData from '@quickapp/utils/lib/globalData'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data: {
    adList: [],
    refreshTag: 0,
    bgStyle: '',
    showService: false,
  },
  onInit() {
    this.$on('onPageBack', this.onPageBack)
    this.$on('onPageRefresh', this.showAd)
  },
  onReady() {
    adsdk.onAdPrepared(() => {
      this.showAd()
      this.showService = config.getAdConfig()['show_complaint']
      let timeout = config.getAdConfig()['native_refresh']
      if (timeout >= 200) {
        setInterval(() => {
          try {
            if (!this.$visible) return
            let isAllShow = this.adList.every(item => item.showTime > 0)
            if (!isAllShow || this.adList.length <= 0) return
            if (adsdk.adCacheList.length > 0) {
              let item = this.adList.reduce((smallest, current) => {
                return current.showTime < smallest.showTime ? current : smallest
              })
              item.outdated = true
              this.$broadcast('updateAd')
            }
          } catch (e) {}
        }, timeout)
      }
    })
  },
  showAd() {
    this.adList.forEach(item => {
      item.outdated = true
    })
    this.$broadcast('updateAd')
    setTimeout(() => {
      this.filterAd()
    })
  },
  updateAd() {
    const refreshOnlyClick = config.getAdConfig()['refresh_only_click']
    if (refreshOnlyClick) {
      this.$broadcast('updateAd')
    } else {
      this.showAd()
    }
  },
  filterAd() {
    let adList = this.adList
    if (adList.length === 0) {
      let num = config.getAdConfig()['ad_num'] || 4
      let adParallelNum = config.getAdConfig()['ad_parallel_num'] || 3
      adList = adsdk.createAdRequest(num).map((item, index) => {
        item.parallel = index < adParallelNum
        return item
      })
    }
    let width =
      globalData.deviceInfo.windowWidth || globalData.deviceInfo.screenWidth
    let height =
      globalData.deviceInfo.windowHeight || globalData.deviceInfo.screenHeight
    let screenHeight = height * (750 / width)
    let successSize = adList.filter(item => item.success).length
    this.adList = [
      ...adList.map((item, index, arr) => {
        if (!item.success || successSize <= 0) {
          item.style = 'height: 0;'
        } else {
          item.height = screenHeight / successSize
          item.style = `height: ${item.height}px;`
        }
        return item
      }),
    ]
  },
  onAdClick() {
    this.$emit('adclick')
  },
  onAdLoad() {
    this.filterAd()
  },
  onAdError() {
    this.filterAd()
  },
  onPageBack() {
    // this.bgStyle = `background-image: url('http://fishing-h5.springdance.cn/images/cover/5.webp?v=1');`
    if (debug) {
      showDialog({
        message: adsdk.logs.join('\n'),
      })
    }
  },
}
</script>
<import
  name="ad-block"
  src="@quickapp/business/components/ad-block.ux"
></import>
<import name="service" src="@quickapp/mc-ui/components/service.ux"></import>
<template>
  <div class="wrapper" style="{{bgStyle}}">
    <!--    <div class="bg-img"></div>-->
    <div class="n-img"></div>
    <div class="ad-root">
      <div
        class="item"
        for="{{adList}}"
        style="{{$item.style}}"
        tid="requestId"
      >
        <ad-block
          reqid="{{$item.requestId}}"
          parallel="{{$item.parallel}}"
          @adclick="onAdClick"
          @load="onAdLoad"
          @error="onAdError"
        ></ad-block>
      </div>
    </div>
    <text class="bg-text">网络错误，点击屏幕重试</text>
    <div class="ad-clickbtn-1"></div>
    <div class="ad-clickbtn-2"></div>
    <service if="showService" show-service="{{showService}}"></service>
  </div>
</template>

<style scoped lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-size: cover;
  position: relative;
  background-color: black;

  .bg-img {
    position: absolute;
    left: 0;
    top: 348px;
    width: 750px;
    height: 804px;
    background-image: url('http://fishing-h5.springdance.cn/images/cover/bg_v2.webp');
    background-size: cover;
  }

  .ad-clickbtn-1 {
    position: absolute;
    left: 73px;
    top: 91px;
    width: 22px;
    height: 4px;
    transform: rotate(135deg);
    transform-origin: 0 0;
    background-color: #ffffff;
    align-items: center;
    justify-content: center;
  }

  .ad-clickbtn-2 {
    position: absolute;
    left: 57px;
    top: 105px;
    width: 22px;
    height: 4px;
    transform: rotate(45deg);
    transform-origin: 0 0;
    background-color: #ffffff;
    align-items: center;
    justify-content: center;
  }

  .bg-text {
    position: absolute;
    left: 210px;
    top: 800px;
    color: #eeeeee;
  }

  .n-img {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 750px;
    height: 88px;
    background-image: url('http://fishing-h5.springdance.cn/images/cover/n_v1.webp');
    background-size: cover;
  }

  .ad-root {
    width: 100%;
    flex: 1;
    flex-direction: column;

    .item {
      width: 100%;
    }
  }
}
</style>
