<script>
import adsdk from '../lib/adsdk'

export default {
  data() {
    return {
      adComponent: '',
      showAdComponent: true,
    }
  },
  props: {
    reqid: {
      type: String,
    },
    parallel: {
      type: Boolean,
      default: false,
    },
    showSplashAd: {
      type: Boolean,
      default: false,
    },
  },
  onInit() {
    this.$on('updateAd', this.updateAd)
    this.$on('closeAd', this.closeAd)
    this.$on('downloadclick', this.onDownloadClick)
  },
  onReady() {
    this.loadAd()
  },
  async loadAd() {
    if (!this.reqid) return
    let adRequest = adsdk.getAdRequest(this.reqid)
    if (!adRequest) return
    this.adObj = await adRequest.load(this.parallel)
    if (!adRequest.success) {
      this.$emit('load')
    } else {
      this.adComponent = adRequest.component
    }
  },
  async updateAd() {
    if (!this.reqid) return
    let adRequest = adsdk.getAdRequest(this.reqid)
    if (!adRequest) return
    if (adRequest.outdated) {
      adRequest.destroy()
      this.showAdComponent = false
      this.$emit('load')
      this.adObj = await adRequest.load(false)
      if (adRequest.success) {
        setTimeout(() => {
          this.showAdComponent = true
        })
      }
    }
  },
  onAdClick() {
    let adRequest = adsdk.getAdRequest(this.reqid)
    if (!adRequest) return
    adRequest.outdated = true
    this.$emit('adclick')
  },
  onAdLoad() {
    this.$emit('load')
  },
  onAdShow() {
    this.$emit('show')
    let adRequest = adsdk.getAdRequest(this.reqid)
    if (!adRequest) return
    adRequest.showTime = Date.now()
  },
  onAdError() {
    let adRequest = adsdk.getAdRequest(this.reqid)
    if (!adRequest) return
    adRequest.destroy()
    this.$emit('error')
  },
  onAdClose() {
    this.$emit('close')
  },
  onDownloadClick() {
    let adRequest = adsdk.getAdRequest(this.reqid)
    if (!adRequest) return
    adRequest.outdated = true
  },
  closeAd() {
    let adRequest = adsdk.getAdRequest(this.reqid)
    if (!adRequest) return
    adRequest.destroy()
  },
}
</script>
<import
  name="oppo-ad2"
  src="@quickapp/business/components/oppo/oppo-ad2.ux"
></import>
<import
  name="vivo-ad2"
  src="@quickapp/business/components/vivo/vivo-ad2.ux"
></import>
<!--<import
  name="ylh-ad-custom"
  src="@quickapp/business/components/ylh/ylh-ad-custom.ux"
></import>
<import
  name="baidu-ad"
  src="@quickapp/business/components/baidu/baidu-ad.ux"
></import>-->
<import
  name="xiaomi-ad2"
  src="@quickapp/business/components/xiaomi/xiaomi-ad2.ux"
></import>
<import
  name="huawei-ad"
  src="@quickapp/business/components/huawei/huawei-ad.ux"
></import>
<import
  name="honor-ad2"
  src="@quickapp/business/components/honor/honor-ad2.ux"
></import>
<template>
  <div>
    <component
      is="{{adComponent}}"
      if="{{showAdComponent}}"
      reqid="{{reqid}}"
      show-splash-ad="{{showSplashAd}}"
      @adclick="onAdClick"
      @load="onAdLoad"
      @show="onAdShow"
      @error="onAdError"
      @close="onAdClose"
    ></component>
  </div>
</template>

<style scoped lang="less"></style>
