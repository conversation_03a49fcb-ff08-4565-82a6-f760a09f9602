import ad from '@service.ad'
import adsdk from '../adsdk'
import { trackEvent } from '../index'
import config from '../config'

class HuaweiAd {
  adData = null
  ecpm = 0
  ecpm_floor = 0
  type = 'standard'
  nativeAd = null

  constructor(adUnitId, type, ecpm_floor) {
    this.adUnitId = adUnitId
    this.type = type
    this.ecpm_floor = ecpm_floor
  }

  preloadAd() {
    return new Promise((resolve, reject) => {
      this.nativeAd = ad.createNativeAd({ adUnitId: this.adUnitId })
      this.nativeAd.onLoad(data => {
        this.adData = data.adList[0]
        if (this.adData) {
          adsdk.log('huaweiAdRequest ad loaded: ' + this.adUnitId)
          if (this.adData.ecpm > 0) {
            this.ecpm = this.adData.ecpm
          } else {
            this.ecpm = this.ecpm_floor
          }
          resolve(this.adData)
        }
      })
      this.nativeAd.onError(e => {
        console.error('load ad error:' + JSON.stringify(e))
        adsdk.log(
          `huaweiAdRequest fail ${this.adUnitId} data: ${JSON.stringify(e)}`
        )
        try {
          const failReportPercent = config.getAdConfig()['fail_report_percent']
          if (failReportPercent && Math.random() * 100 < failReportPercent) {
            trackEvent({
              category: 'advertise',
              action: 'fail',
              opt_label: 'native_ad',
              opt_extra: {
                unit_id: this.adUnitId,
                err_msg: e,
              },
            })
          }
        } catch (e) {}
        resolve(null)
      })
      this.nativeAd.load()
    })
  }

  isDownloadAd() {
    return (
      this.adData.clickBtnTxt.includes('安装') ||
      this.adData.clickBtnTxt.includes('下载')
    )
  }

  reportNativeShow() {
    try {
      this.nativeAd?.reportAdShow({ adId: this.adData.adId })
    } catch (e) {}
  }

  reportNativeClick() {
    try {
      this.nativeAd?.reportAdClick({
        adId: this.adData.adId,
      })
    } catch (e) {}
  }

  destroy() {
    try {
      this.nativeAd?.destroy()
    } catch (e) {}
  }

  sendWinNotification(lossEcpm) {}

  sendLossNotification(winEcpm) {}
}

export default HuaweiAd
