import ad from '@service.ad'
import config from './config'
import OppoAd from './ads/oppoAd'
import { trackEvent } from './index'
import VivoAd from './ads/vivoAd'
import YlhAd from './ads/ylhAd'
import BaiduAd from './ads/baiduAd'
import HonorAd from './ads/honorAd'
import XiaomiAd from './ads/xiaomiAd'
// import YLHQuickAppSDK from 'ylh-quick-app-ad-sdk'
// import UNION_AD_SDK from 'union-quick-app-ad/app'
import globalData from '@quickapp/utils/lib/globalData'
import { storage } from '@quickapp/utils'
import HuaweiAd from './ads/huaweiAd'

// 常量定义
const DEBUG = process.env.NODE_ENV !== 'production'

// 配置常量
const CONSTANTS = {
  DEFAULT_DELAY: 50,
  CACHE_CHECK_DELAY: 200,
  CACHE_FULL_DELAY: 500,
  PRELOAD_DELAY: 1500,
  RANDOM_DELAY_MAX: 100,
  DEFAULT_PARALLEL_NUM: 4,
  DEFAULT_CACHE_NUM: 4,
  DEFAULT_AD_NUM: 4,
}

// 广告网络映射
const AD_NETWORKS = {
  ylh: 'ylh',
  baidu: 'baidu',
}

// 广告提供商映射
const AD_PROVIDERS = {
  oppo: 'oppo',
  vivo: 'vivo',
  huawei: 'huawei',
  honor: 'honor',
  xiaomi: 'xiaomi',
}

// 广告组件映射
const AD_COMPONENTS = new Map([
  [BaiduAd, 'baidu-ad'],
  [HuaweiAd, 'huawei-ad'],
  [HonorAd, 'honor-ad2'],
  [OppoAd, 'oppo-ad2'],
  [VivoAd, 'vivo-ad2'],
  [XiaomiAd, 'xiaomi-ad2'],
  [YlhAd, 'ylh-ad-custom'],
])

// 广告类构造器映射
const AD_CONSTRUCTORS = new Map([
  [
    AD_NETWORKS.ylh,
    (context, appId, unitId, type) => new YlhAd(context, appId, unitId, type),
  ],
  [
    AD_NETWORKS.baidu,
    (context, appId, unitId, type) => new BaiduAd(context, appId, unitId, type),
  ],
  [
    AD_PROVIDERS.oppo,
    (unitId, type, ecpmFloor) => new OppoAd(unitId, type, ecpmFloor),
  ],
  [
    AD_PROVIDERS.vivo,
    (unitId, type, ecpmFloor) => new VivoAd(unitId, type, ecpmFloor),
  ],
  [
    AD_PROVIDERS.huawei,
    (unitId, type, ecpmFloor) => new HuaweiAd(unitId, type, ecpmFloor),
  ],
  [
    AD_PROVIDERS.honor,
    (unitId, type, ecpmFloor) => new HonorAd(unitId, type, ecpmFloor),
  ],
  [
    AD_PROVIDERS.xiaomi,
    (unitId, type, ecpmFloor) => new XiaomiAd(unitId, type, ecpmFloor),
  ],
])

// 工具函数
function sortByEcpm(a, b) {
  return b.ecpm - a.ecpm
}

/**
 * 验证广告参数
 * @param {Object} params - 广告参数
 * @returns {boolean} 是否有效
 */
function validateAdParams(params) {
  return params && typeof params === 'object' && params.unit_id
}

/**
 * 创建广告实例
 * @param {Object} context - 上下文
 * @param {Object} params - 广告参数
 * @returns {Object|null} 广告实例
 */
function createAd(context, params) {
  if (!validateAdParams(params)) {
    return null
  }

  const { unit_id, type, ecpm_floor, network, app_id } = params

  // 优先处理指定网络
  if (network && AD_CONSTRUCTORS.has(network)) {
    const constructor = AD_CONSTRUCTORS.get(network)
    return constructor(context, app_id, unit_id, type)
  }

  // 根据提供商创建广告
  const providerKey =
    AD_PROVIDERS[globalData.$adsdk.provider()] || AD_PROVIDERS.oppo

  if (AD_CONSTRUCTORS.has(providerKey)) {
    const constructor = AD_CONSTRUCTORS.get(providerKey)
    return constructor(unit_id, type, ecpm_floor)
  }

  // 默认返回 OPPO 广告
  return new OppoAd(unit_id, type, ecpm_floor)
}

/**
 * 获取广告组件名称
 * @param {Object} adObj - 广告对象
 * @returns {string} 组件名称
 */
function getAdComponent(adObj) {
  if (!adObj) {
    return 'oppo-ad2'
  }

  for (const [AdClass, componentName] of AD_COMPONENTS) {
    if (adObj instanceof AdClass) {
      return componentName
    }
  }

  return 'oppo-ad2'
}

/**
 * 随机排序数组
 * @param {Array} array - 要排序的数组
 * @returns {Array} 排序后的数组
 */
function shuffleArray(array) {
  return [...array].sort(() => 0.5 - Math.random())
}

/**
 * 初始化YLH SDK
 * @param {Object} context - 上下文
 * @param {string} appId - 应用ID
 */
function initYlhSdk(context, appId) {
  if (!appId || context.$app.$def.ylh_sdk) {
    return
  }

  /*try {
    const sdkConfig = { appId }
    new YLHQuickAppSDK(context.$app, sdkConfig)
  } catch (error) {
    console.log('ylh_sdk init error', error)
  }*/
}

/**
 * 获取原生广告组
 * @param {Object} context - 上下文
 * @returns {Array} 广告组数组
 */
function getNativeAdGroup(context) {
  const adConfig = config.getAdConfig()
  const globalConfig = config.getConfig()

  // 获取各种广告配置
  const nativeAll = adConfig['native_all'] || []
  const waterfallAll = adConfig['waterfall_all'] || []
  const ylhAll = globalConfig.ad?.ylh?.['native_all'] || []
  const baiduAll = globalConfig.ad?.baidu?.['native_all'] || []
  const ylhAppId = globalConfig.ad?.ylh?.app_id
  const baiduAppId = globalConfig.ad?.baidu?.app_id

  if (nativeAll.length <= 0) {
    return []
  }

  // 初始化YLH SDK
  initYlhSdk(context, ylhAppId)

  // 随机排序所有广告数组
  const shuffledNative = shuffleArray(nativeAll)
  const shuffledWaterfall = shuffleArray(waterfallAll)
  const shuffledYlh = shuffleArray(ylhAll)
  const shuffledBaidu = shuffleArray(baiduAll)

  // 构建广告组
  const groups = []
  for (let i = 0; i < shuffledNative.length; i++) {
    const group = [
      { unit_id: shuffledNative[i], type: 'bidding' },
      shuffledWaterfall[i],
      {
        unit_id: shuffledYlh[i],
        type: 'bidding',
        network: AD_NETWORKS.ylh,
        app_id: ylhAppId,
      },
      {
        unit_id: shuffledBaidu[i],
        type: 'bidding',
        network: AD_NETWORKS.baidu,
        app_id: baiduAppId,
      },
    ]
    groups.push(group)
  }

  return groups
}

/**
 * 等待广告缓存可用
 * @param {Object} adsdk - 广告SDK实例
 * @returns {Promise<Object>} 广告对象
 */
async function waitForAdCache(adsdk) {
  while (true) {
    const adCache = adsdk.adCacheList.pop()
    if (adCache) {
      return adCache
    }
    await delay(CONSTANTS.DEFAULT_DELAY)
  }
}

/**
 * 创建原生广告请求对象
 * @param {Object} context - 上下文
 * @param {Array} items - 广告项目数组
 * @returns {Object} 广告请求对象
 */
function getNativeAdRequest(context, items) {
  return {
    requestId: `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
    unitId: null,
    adObj: null,
    component: null,
    success: false,
    outdated: false,
    _loading: false,
    _loadPromise: null,
    showTime: 0,

    async load(parallel) {
      // 如果正在加载，等待现有的Promise
      if (this._loading && this._loadPromise) {
        await this._loadPromise
        return this.adObj
      }

      // 如果已经有广告对象，直接返回
      if (this.adObj) {
        return this.adObj
      }

      // 设置加载标志并创建新的Promise
      this._loading = true
      this._loadPromise = this._loadAd(context, parallel)

      try {
        return await this._loadPromise
      } finally {
        this._loading = false
      }
    },

    destroy() {
      if (this.adObj) {
        globalData.$adsdk.additionRequestList.unshift(this.adObj.requestAdGroup)
        if (this.adObj.destroy) {
          this.adObj.destroy()
        }
      }
      this.adObj = null
      this.component = null
      this.success = false
      this.outdated = false
      this._loading = false
      this._loadPromise = null
      this.showTime = 0
    },

    async _loadAd(context, parallel) {
      const adsdk = globalData.$adsdk

      try {
        // 尝试从缓存获取广告
        let adCache = adsdk.adCacheList.pop()

        // 如果缓存为空且在并行数量范围内，直接请求广告
        if (!adCache && parallel) {
          adCache = await adBidding(context, items)
        }

        // 如果仍然没有广告，等待缓存
        if (!adCache) {
          adCache = await waitForAdCache(adsdk)
        }

        // 设置广告对象和相关属性
        this.adObj = adCache
        this.component = getAdComponent(this.adObj)
        this.success = this.adObj != null && this.adObj.adData != null
        this.unitId = this.adObj?.adUnitId

        return this.adObj
      } catch (error) {
        adsdk.log(`ad load error: ${error}`)
        return null
      }
    },
  }
}

/**
 * 处理竞价失败通知
 * @param {Array} lossList - 失败的广告列表
 * @param {number} winEcpm - 获胜广告的ECPM
 */
function handleLossNotifications(lossList, winEcpm) {
  for (const lossData of lossList) {
    if (lossData.type === 'bidding' && lossData.ecpm >= 0) {
      lossData.sendLossNotification(winEcpm)
    }
  }
}

/**
 * 检查ECPM底价
 * @param {Object} winData - 获胜的广告数据
 * @param {number} ecpmFloor - ECPM底价
 * @returns {boolean} 是否通过底价检查
 */
function checkEcpmFloor(winData, ecpmFloor) {
  return !(ecpmFloor && winData.ecpm > 0 && winData.ecpm < ecpmFloor)
}

/**
 * 检查广告类型
 * @param {Object} winData - 获胜的广告数据
 * @returns {boolean} 是否下载广告类型
 */
function checkShowDownloadAd(winData) {
  let showDownloadAdOnly = config.getAdConfig()['show_app_ad_only']
  if (showDownloadAdOnly) {
    return winData.isDownloadAd()
  } else {
    return true
  }
}

/**
 * 广告竞价函数
 * @param {Object} context - 上下文
 * @param {Array} items - 广告项目数组
 * @returns {Promise<Object|null>} 获胜的广告对象
 */
async function adBidding(context, items) {
  if (!items) {
    return null
  }

  // 检查广告限制
  if (!(await globalData.$adsdk.checkAdLimit())) {
    globalData.appStateFlow.value = 'onAdLimit'
    return null
  }

  try {
    // 并行加载所有广告
    const adPromises = items.map(async item => {
      if (!item) return null

      const adObj = createAd(context, item)
      if (!adObj) return null

      try {
        await adObj.preloadAd()
      } catch (e) {}
      return adObj
    })

    const adResults = await Promise.all(adPromises)
    const validAds = adResults
      .filter(adObj => adObj != null && adObj.adData != null)
      .sort(sortByEcpm)

    // 记录广告请求
    globalData.$adsdk.onAdRequest()

    if (validAds.length <= 0) {
      return null
    }

    const winData = validAds[0]
    const lossList = validAds.slice(1)
    const nativeEcpmFloor = config.getAdConfig()['native_ecpm_floor']

    // 处理竞价失败通知
    handleLossNotifications(lossList, winData.ecpm)

    // 检查ECPM底价
    if (!checkEcpmFloor(winData, nativeEcpmFloor)) {
      if (winData.type === 'bidding') {
        winData.sendLossNotification(nativeEcpmFloor)
      }
      return null
    }

    if (!checkShowDownloadAd(winData)) {
      if (winData.type === 'bidding') {
        winData.sendLossNotification()
      }
      return null
    }

    // 发送获胜通知
    if (winData.type === 'bidding' && winData.ecpm > 0) {
      winData.sendWinNotification(lossList[0]?.ecpm)
    }

    winData.requestAdGroup = items
    return winData
  } catch (error) {
    globalData.$adsdk.log(`adBidding error: ${error}`)
    return null
  }
}

/**
 * 创建开屏广告事件数据
 * @param {string} action - 动作类型
 * @param {Object} data - 广告数据
 * @returns {Object} 事件数据
 */
function createSplashEventData(action, data) {
  return {
    category: 'advertise',
    action,
    opt_label: 'splash_ad',
    opt_extra: {
      event_time: Date.now(),
      unit_id: data?.adId,
      ecpm: data?.ecpm,
    },
  }
}

/**
 * 开屏广告监听器
 */
function splashListener() {
  if (!ad.onSplashStatus) return
  try {
    ad.onSplashStatus({
      reserved: false,
      callback: ({ statusCode, data }) => {
        let eventData = null

        switch (statusCode) {
          case '0': // 曝光
            eventData = createSplashEventData('exposure', data)
            break
          case '100': // 点击
            eventData = createSplashEventData('click', data)
            break
          default:
            return
        }

        if (eventData) {
          trackEvent(eventData)
        }
      },
    })
  } catch (error) {
    globalData.$adsdk.log(`onSplashStatus error: ${error}`)
  }
}

/**
 * 延迟函数
 * @param {number} timeout - 延迟时间（毫秒）
 * @returns {Promise<void>}
 */
async function delay(timeout) {
  return new Promise(resolve => {
    setTimeout(resolve, timeout)
  })
}

/**
 * 获取今天的日期字符串
 * @returns {string} 格式为YYYYMMDD的日期字符串
 */
function today() {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}${month}${day}`
}

// 创建广告SDK实例
if (!globalData.$adsdk) {
  globalData.$adsdk = {
    // 状态属性
    logs: [],
    adRequestList: [],
    adCacheList: [],
    additionRequestList: [],
    startAdditionAdFlag: false,
    adRequestCount: 0,
    adShowCount: 0,
    biddingCount: 0,
    adPrepareCallback: [],

    /**
     * 初始化广告SDK
     * @param {Object} context - 上下文
     */
    async onCreate(context) {
      // 初始化开屏广告监听器
      splashListener()

      // 初始化联盟广告SDK
      /*try {
        new UNION_AD_SDK(context)
      } catch (error) {
        this.log(`UNION_AD_SDK init error: ${error}`)
      }*/

      // 加载今日广告统计数据
      const todayKey = today()
      this.adRequestCount = (await storage.get(`ad_request_${todayKey}`)) || 0
      this.adShowCount = (await storage.get(`ad_show_${todayKey}`)) || 0
    },

    /**
     * 开始广告预加载
     * @param {Object} context - 上下文
     */
    startAdPreload(context) {
      if (this.startAdditionAdFlag) {
        return
      }
      this.initAdRequestList()
      this.startAdditionAdFlag = true
      this.startLoadAdQueue(context)
      this.adPrepareCallback.forEach(callback => callback())
      this.adPrepareCallback = []
    },

    onAdPrepared(callback) {
      if (!this.startAdditionAdFlag) {
        this.adPrepareCallback.push(callback)
      } else {
        callback()
      }
    },

    /**
     * 记录广告请求
     */
    onAdRequest() {
      this.adRequestCount++
      const key = `ad_request_${today()}`

      storage
        .set(key, this.adRequestCount)
        .then(() => {
          this.log(`ad_request ${key} ${this.adRequestCount}`)
        })
        .catch(error => {
          this.log(`Failed to save ad request count: ${error}`)
        })
    },

    /**
     * 记录广告展示
     */
    onAdShow() {
      // 添加随机延迟以避免并发问题
      const randomDelay = Math.trunc(CONSTANTS.RANDOM_DELAY_MAX * Math.random())

      setTimeout(() => {
        this.adShowCount++
        const key = `ad_show_${today()}`

        storage
          .set(key, this.adShowCount)
          .then(() => {
            this.log(`ad_show ${key} ${this.adShowCount}`)
          })
          .catch(error => {
            this.log(`Failed to save ad show count: ${error}`)
          })
      }, randomDelay)
    },
    /**
     * 检查广告限制
     * @returns {Promise<boolean>} 是否允许请求广告
     */
    async checkAdLimit() {
      const todayKey = today()
      const adRequestCount = (await storage.get(`ad_request_${todayKey}`)) || 0
      const adShowCount = (await storage.get(`ad_show_${todayKey}`)) || 0

      const adConfig = config.getAdConfig()
      const nativeRequestMax = adConfig['native_request_max']
      const nativeShowMax = adConfig['native_show_max']

      // 检查请求次数限制
      if (nativeRequestMax && adRequestCount > nativeRequestMax) {
        return false
      }

      // 检查展示次数限制
      if (nativeShowMax && adShowCount > nativeShowMax) {
        return false
      }

      return true
    },

    /**
     * 开始加载广告队列
     * @param {Object} context - 上下文
     */
    async startLoadAdQueue(context) {
      const maxNum =
        config.getAdConfig()['ad_cache_num'] || CONSTANTS.DEFAULT_CACHE_NUM
      while (this.startAdditionAdFlag) {
        try {
          // 检查广告限制
          if (!(await this.checkAdLimit())) {
            globalData.appStateFlow.value = 'onAdLimit'
            break
          }

          // 检查是否有待处理的请求
          if (this.additionRequestList.length <= 0) {
            await delay(CONSTANTS.CACHE_CHECK_DELAY)
            continue
          }

          // 检查应用是否可见
          if (!globalData.appVisible) {
            await delay(CONSTANTS.CACHE_CHECK_DELAY)
            continue
          }

          // 检查缓存是否已满
          if (this.adCacheList.length >= maxNum) {
            await delay(CONSTANTS.CACHE_FULL_DELAY)
            continue
          }

          // 处理广告请求
          const items = this.additionRequestList.pop()
          const adObj = await adBidding(context, items)
          if (adObj && adObj.adData != null) {
            this.adCacheList.push(adObj)
            this.adCacheList.sort((a, b) => a.ecpm - b.ecpm)
          } else {
            // 如果加载失败，将请求重新放回队列前端
            this.additionRequestList.unshift(items)
          }

          await delay(CONSTANTS.DEFAULT_DELAY)
        } catch (error) {
          this.log(`startLoadAdQueue error: ${error} ${error.stack}`)
          await delay(CONSTANTS.DEFAULT_DELAY)
        }
      }
    },

    initAdRequestList(context) {
      const groups = getNativeAdGroup(context)
      this.additionRequestList = [...groups]
      this.adRequestList = groups.map(items =>
        getNativeAdRequest(context, items)
      )
    },

    createAdRequest(num) {
      let requestList = this.adRequestList.splice(0, num)
      this.adRequestList.push(...requestList)
      return requestList
    },

    getAdRequest(requestId) {
      return this.adRequestList.find(item => item.requestId === requestId)
    },

    /**
     * 记录日志
     * @param {string} message - 日志消息
     */
    log(message) {
      if (!DEBUG) return
      const logEntry = `${message}`

      this.logs.unshift(logEntry)
      console.log(logEntry)

      // 限制日志数量，避免内存泄漏
      if (this.logs.length > 1000) {
        this.logs = this.logs.slice(0, 500)
      }
    },

    provider() {
      if (globalData.adProvider) {
        return globalData.adProvider
      }
      try {
        let provider = ad.getProvider()?.toLowerCase()
        if (provider && !provider.includes('refuse')) {
          globalData.adProvider = provider
          return provider
        }
      } catch (e) {}
      return globalData.deviceInfo?.brand?.toLowerCase() || 'oppo'
    },
  }
}

export default globalData.$adsdk
