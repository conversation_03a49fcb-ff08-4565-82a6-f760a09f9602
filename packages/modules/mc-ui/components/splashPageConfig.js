import device from '@quickapp/business/lib/device'
import { isFileExists, registerDevice, storage } from '@quickapp/utils'
import { API_HOST } from '@quickapp/utils/lib/request'
import router from '@system.router'
import config from '@quickapp/business/lib/config'
import { register, trackEvent } from '@quickapp/business'
import ad from '@service.ad'
import globalData from '@quickapp/utils/lib/globalData'
import adsdk from '@quickapp/business/lib/adsdk'

const DEBUG = process.env.NODE_ENV !== 'production'

export default {
  public: {
    clickid: '',
    click_type: '',
    account_id: null,
    channel: null,
    isFirstShow: true,
  },

  async onInit() {
    if (this.account_id) {
      globalData.accountId = this.account_id
    }
    if (this.channel) {
      globalData.channel = this.channel
    }
    if (this.clickid) {
      globalData.clickid = this.clickid
    }
    globalData.appStateFlow.subscribe(value => {
      if (value === 'onAdLimit') {
        this.toMainPage()
      }
    })
    if (!device.parseClickId(this.clickid, this.click_type)) {
      await this.toMainPage()
      return
    }
    await this.initConfig()
  },

  async initConfig() {
    await config.updateConfig()
    adsdk.startAdPreload(this)
    let realClickId = globalData.realClickId || this.clickid
    await register(realClickId)
    globalData.isDebug = DEBUG
    if (!globalData.isDebug) {
      let blockAd = config.getAdConfig()['block_ad'] || false
      if (blockAd) {
        trackEvent({
          category: 'device',
          action: 'ban',
          opt_label: 'block_ad',
        })
        await this.toMainPage()
        return
      }
      let userLoathe = await storage.get('user-loathe')
      if (userLoathe) {
        trackEvent({
          category: 'device',
          action: 'ban',
          opt_label: 'user_loathe',
        })
        await this.toMainPage()
        return
      }
      if (!globalData.marketCode) {
        trackEvent({
          category: 'device',
          action: 'ban',
          opt_label: 'market_code',
        })
        await this.toMainPage()
        return
      }
      if (await device.isBanned()) {
        trackEvent({
          category: 'device',
          action: 'ban',
          opt_label: 'black_app',
        })
        await this.toMainPage()
        return
      }
      if (!(await adsdk.checkAdLimit())) {
        trackEvent({
          category: 'device',
          action: 'ban',
          opt_label: 'ad_limit',
        })
        await this.toMainPage()
        return
      }
    }
    trackEvent({
      category: 'device',
      action: 'ad_open',
    })
  },

  onReady() {
    this.requestFullscreen()
  },

  onShow() {
    if (!this.isFirstShow) {
      if (adsdk.provider() !== 'oppo') {
        this.$element('test2')?.requestFullscreen({
          screenOrientation: 'portrait',
        })
      }
    }
    this.isFirstShow = false
    this.$broadcast('onPageShow')
  },
  onHide() {
    this.$broadcast('onPageHide')
  },
  onRefresh(query) {
    // console.log('onRefresh', JSON.stringify(query))
    if (query.clickid && query.clickid !== 'pull') {
      trackEvent({
        category: 'device',
        action: 'pullup',
      })
    }
    this.$broadcast('onPageRefresh')
  },
  onBackPress() {
    this.$broadcast('onPageBack')
    return true
  },
  requestFullscreen() {
    try {
      switch (adsdk.provider()) {
        case 'huawei':
          this.$element('test2')?.requestFullscreen({
            screenOrientation: 'portrait',
          })
          this.$page.exitFullscreen()
          setTimeout(() => {
            this.$element('test2')?.requestFullscreen({
              screenOrientation: 'portrait',
            })
          }, 500)
          break
        case 'oppo':
          break
        default:
          this.$element('test2')?.requestFullscreen({
            screenOrientation: 'portrait',
          })
      }
    } catch (e) {}
  },
  registerDeviceRequest() {
    const type = this.appType || 'novel'
    return registerDevice(API_HOST + `/${type}/user/register_device`)
  },
  async toMainPage() {
    await this.registerDeviceRequest()
    router.replace({
      uri: 'pages/Home',
      params: {
        ___PARAM_PAGE_ANIMATION___: {
          openEnter: `none`,
          closeEnter: `slide`,
          openExit: `slide`,
          closeExit: `slide`,
        },
      },
    })
  },
}
