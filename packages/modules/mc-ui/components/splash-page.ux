<import
  name="recommend"
  src="@quickapp/business/components/recommend.ux"
></import>
<import
  name="recommend-vivo"
  src="@quickapp/business/components/vivo/recommend.ux"
></import>
<import
  name="splashad"
  src="@quickapp/business/components/splashad.ux"
></import>
<import name="service" src="@quickapp/mc-ui/components/service.ux"></import>
<template>
  <div class="wrapper">
    <stack id="stack">
      <web id="web" src="{{webSrc}}" class="web" if="showWeb"></web>
      <component
        id="recommend"
        is="recommend"
        @adclick="onAdClick"
        @close="onAdClose"
      ></component>
    </stack>
  </div>
</template>

<script>
import config from '@quickapp/business/lib/config'
import ad from '@service.ad'
import rewardedAdConfig from '@quickapp/business/lib/rewarded/rewardedAdConfig'
import adsdk from '@quickapp/business/lib/adsdk'
import { trackEvent } from '@quickapp/business'
import globalData from '@quickapp/utils/lib/globalData'
import HonorAd from '@quickapp/business/lib/ads/honorAd'
import sensor from '@system.sensor'
import image from '@system.image'
import device from '@quickapp/business/lib/device'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data: {
    hideBefore: false,
    rewarded: null,
    webSrc: '',
    timeout1: 0,
    message: '',
    showWeb: false,
    recommend: '',
  },

  props: {
    clickId: {
      type: String,
    },
    appType: {
      type: String,
    },
    clickType: {
      type: String,
    },
  },

  computed: {
    provider() {
      return adsdk.provider()
    },
  },
  async onInit() {
    globalData.deviceInfo = await device.getDeviceInfo()
    this.$on('onPageShow', this.onPageShow)
    this.$on('onPageHide', this.onPageHide)
    if (this.provider === 'vivo') {
      this.recommend = 'recommend-vivo'
    } else if (this.provider === 'oppo') {
      this.recommend = 'splashad'
    } else {
      this.recommend = 'recommend'
    }
  },
  onReady() {
    if (this.provider === 'oppo') {
      this.$element('stack')?.requestFullscreen()
    }
    adsdk.onAdPrepared(() => {
      this.webSrc = this.buildWebSrc()
      this.showBanner()
      this.rewarded = rewardedAdConfig(this)
      this.rewarded.onShow()
    })
  },
  showBanner() {
    if (this.provider === 'honor') {
      let bannerAdId = config.getAdConfig()['banner_ad']
      let honorAd = new HonorAd(bannerAdId)
      honorAd.showBannerAd({
        top: 30,
        left: this.$page.windowWidth - 360,
        width: 360,
        height: 202,
      })
    }
  },
  buildWebSrc() {
    let pullConfig = config.getAdConfig()['pull_back'] || {}
    const webSrcHeader = 'data:text/html,'
    let href = ''
    if (pullConfig['type'] === 1) {
      href = 'weixin://wap/pay/unknown'
    } else if (pullConfig['type'] === 2) {
      href = 'hap://settings/nfc_manager'
    } else if (pullConfig['type'] === 3) {
      href = 'oppomarket://details?' + Math.random()
    } else if (pullConfig['type'] === 4) {
      let pullPackage = pullConfig.package
      if (!pullPackage) {
        pullPackage = __MANIFEST__.package
      }
      let link = encodeURIComponent(
        `hap://app/${pullPackage}/pages/Splash?click_type=oneJump&channel=${
          globalData.channel
        }&account_id=${globalData.accountId}&clickid=${
          globalData.clickid || 'pull'
        }`
      )
      href = `hiapp://com.huawei.appmarket?activityName=activityUri|webview.activity&params={"params":[{"name":"url","type":"String","value":"${link}"},{"name":"uri","type":"String","value":"external_webview"}]}`
    } else if (pullConfig['type'] === 5) {
      href = 'hap://applet/preview?pkg=wl&key=1'
    } else {
      return pullConfig['url2']
    }
    const webSrcContent = `<!DOCTYPE html><html><body><div></div>\x3Cscript>location.href = '${href}'\x3C/script></body></html>`
    return webSrcHeader + encodeURIComponent(webSrcContent)
  },
  async onPageShow() {
    clearTimeout(this.timeout1)
    this.showWeb = false
    adsdk.log('page show')
    if (this.hideBefore) {
      this.updateRecommend()
    }
  },
  onDownloadClick() {
    setTimeout(() => {
      this.$child('recommend')?.showAd()
    }, 500)
  },
  updateRecommend() {
    try {
      this.$child('recommend')?.updateAd()
    } catch (e) {}
    this.rewarded?.onShow()
  },
  onAdClick() {
    this.rewarded?.showRewardedCache()
  },
  onAdClose() {
    if (this.provider === 'oppo') {
      this.recommend = 'recommend'
    }
  },
  onPageHide() {
    adsdk.log('page hide')
    this.hideBefore = true
    this.pullBackNew()
  },
  pullBackNew() {
    clearTimeout(this.timeout1)
    let pullConfig = config.getAdConfig()['pull_back']
    console.log('pullConfig: ', JSON.stringify(pullConfig))
    if (!pullConfig) return
    let timeout = pullConfig.timeout || 1500
    let pullCount = globalData.pullCount || 0
    if (pullCount >= pullConfig.max) return
    if (this.provider === 'honor') {
      if (pullConfig['type'] === 1) {
        setTimeout(() => {
          // console.log('ad honor handling pull back')
          sensor.subscribeStepCounter({
            callback: function (ret) {
              console.log(`ad honor handling callback, steps = ${ret.steps}`)
            },
            fail: function (data, code) {
              console.log(`ad honor handling fail, code = ${code}`)
            },
          })
        })
      } else {
        image.editImage({
          uri: 'internal://cache/test.png',
        })
      }
      setTimeout(() => {
        if (this.$visible) {
          globalData.pullCount = pullCount + 1
          trackEvent({
            category: 'device',
            action: 'pull_back',
          })
        }
      }, 1500)
    } else if (this.provider === 'huawei') {
      setTimeout(() => {
        if (this.$visible) return
        let pullPackage = pullConfig.package
        if (!pullPackage) {
          pullPackage = __MANIFEST__.package
        }
        let link = `hap://app/${pullPackage}/pages/Splash?click_type=oneJump&channel=${globalData.channel}&account_id=${globalData.accountId}&clickid=pull`
        require('@service.jsb').invoke('UriStarter.startActivityByUri', {
          uri: link,
          packageName: '',
          kitName: '',
        })
        setTimeout(() => {
          if (this.$visible) {
            globalData.pullCount = pullCount + 1
            trackEvent({
              category: 'device',
              action: 'pull_back',
            })
          }
        }, 1500)
      }, timeout)
    } else {
      this.timeout1 = setTimeout(() => {
        if (this.$visible) return
        this.showWeb = true
        setTimeout(() => {
          if (this.$visible) {
            globalData.pullCount = pullCount + 1
            trackEvent({
              category: 'device',
              action: 'pull_back',
            })
          }
        }, 1500)
      }, timeout)
    }
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  background-color: white;
}
.web {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  width: 1px;
  height: 1px;
}
</style>
