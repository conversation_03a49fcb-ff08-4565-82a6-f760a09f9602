<import name="weather-page" src="@/weather/index.ux"></import>
<import name="clean-page" src="@/clean/index.ux"></import>
<import name="pet-page" src="@/pet/index.ux"></import>
<import name="shopping-page" src="@/shopping/index.ux"></import>
<import
  name="mine-page"
  src="@quickapp/mc-ui/components/setting-page.ux"
></import>
<template>
  <tabs class="wrapper" @change="handleChange" index="{{currentIndex}}">
    <tab-content class="content">
      <div class="content-box" for="item in pageList" tid="$idx">
        <component
          id="{{item.pageComponent}}"
          is="{{item.pageComponent}}"
          index="{{$idx}}"
          current-index="{{currentIndex}}"
        ></component>
      </div>
    </tab-content>
    <tab-bar class="tab-bar">
      <div for="item in pageList" tid="$idx" class="tab-bar-item">
        <text class="tab-text">{{ item.text }}</text>
      </div>
    </tab-bar>
  </tabs>
</template>

<script>
import { trackEvent } from '@quickapp/business'
import config from '@quickapp/business/lib/config'

export default {
  private: {
    pageList: [
      {
        pageComponent: 'pet-page',
        text: '首页',
      },
      {
        pageComponent: 'weather-page',
        text: '天气',
      },
      {
        pageComponent: 'shopping-page',
        text: '领券',
      },
      {
        pageComponent: 'mine-page',
        text: '我的',
      },
    ],
    currentIndex: 0,
    title: '',
  },

  async onInit() {
    await config.updateConfig()
    let showClean = config.getAdConfig()['show_clean']
    console.log('show clean: ', showClean)
    if (showClean) {
      this.pageList = [
        {
          iconPath: '/clean/assets/ic_tab_clean.webp',
          selectedIconPath: '/clean/assets/ic_tab_clean_active.webp',
          pageComponent: 'clean-page',
          text: '清理',
        },
        ...this.pageList,
      ]
    }
  },

  onShow() {
    trackEvent({
      category: 'page',
      action: 'show',
      opt_label: 'main',
    })
  },

  onBackPress() {
    return false
  },
  handleAdClose() {},

  handleChange(evt) {
    this.currentIndex = evt.index
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  background-color: #f2f4f7;
}

.tab-bar {
  display: flex;
  width: 100%;
  height: 135px;
  background-color: white;
}

.center-tab {
  justify-content: center;
  align-items: center;
  background-color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;

  .icon {
    width: 35px;
    height: 35px;
  }
}

.tab-bar-item {
  width: 20%;
  flex-direction: column;
  align-items: center;
  height: 100px;
  background-color: white;

  .tab-bar-icon {
    width: 52px;
    height: 52px;
  }

  .tab-bar-icon-active {
    width: 60px;
    height: 60px;
  }

  .tab-text {
    text-align: center;
    color: #333333;
    line-height: 100px;
    font-size: 32px;
  }

  .tab-text:active {
    color: #fe0000;
  }
}
</style>
