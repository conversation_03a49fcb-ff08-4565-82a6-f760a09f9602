<import
  name="permission-dialog"
  src="@quickapp/mc-ui/components/permission-dialog.ux"
></import>
<template>
  <div class="page">
    <div class="camera-box">
      <camera
        if="{{showCamera}}"
        id="camera"
        class="camera-com"
        @error="handlerError"
        @camerainitdone="handleCameraInitDone"
      ></camera>
      <!--遮罩-->
      <canvas show="{{showMask}}" id="mask" class="mask-box"></canvas>
    </div>
    <div class="footer">
      <image
        class="footer__pick-img"
        @click="pickImage"
        src="/ocr/assets/ic_pick_img.webp"
      ></image>
      <image
        class="footer__photo"
        @click="handleTakePhoto"
        src="/ocr/assets/ic_take_phone.png"
      ></image>
    </div>
    <!--新手引导-->
    <!--<div if="showNewDialog" class="new-dialog dialog">-->
    <!--  <div class="new-dialog__inner">-->
    <!--    <image-->
    <!--      class="new-dialog__inner__pic"-->
    <!--      src="/ocr/assets/logo.png"-->
    <!--    ></image>-->
    <!--    <text class="new-dialog__inner__title">老照片修复</text>-->
    <!--    <text class="new-dialog__inner__tip">一键智能修复老照片、即刻保存</text>-->
    <!--    <input-->
    <!--      class="new-dialog__inner__btn"-->
    <!--      type="button"-->
    <!--      value="立即拍摄"-->
    <!--      @click="closeNewDialog"-->
    <!--    />-->
    <!--  </div>-->
    <!--</div>-->
    <!--类型选择-->
    <!-- && !showNewDialog-->
    <div if="{{showTypeDialog}}" class="type-dialog dialog">
      <div class="type-dialog__inner">
        <div class="type-dialog__inner__list">
          <div
            class="type-dialog__inner__list__item {{currentTypeIndex === $idx ? 'type-dialog__inner__list__item--active' : '' }}"
            for="item in typeList"
            tid="$idx"
            @click="handleClickType(item, $idx)"
          >
            <!--<div class="type-dialog__inner__list__item__bg"></div>-->
            <image
              class="type-dialog__inner__list__item__icon"
              src="{{item.item_bg}}"
            ></image>
            <div class="type-dialog__inner__list__item__info">
              <text class="type-dialog__inner__list__item__info__title">
                {{ item.title }}
              </text>
              <text class="type-dialog__inner__list__item__info__desc">
                {{ item.typeText }}
              </text>
            </div>
          </div>
        </div>
        <input
          type="button"
          class="type-dialog__inner__btn"
          value="立即拍摄"
          @click="takePhoneSoon"
        />
      </div>
    </div>
    <permission-dialog id="permissionDialog"></permission-dialog>
  </div>
</template>

<script>
import router from '@system.router'
import { showToast, storage } from '@quickapp/utils'
import { fileTypeList } from '../config/files'
import image from '@system.image'
import media from '@system.media'
import adsdk from '@quickapp/business/lib/adsdk'

const KEY_CAMERA = 'camera-first'

export default {
  private: {
    showNewDialog: false,
    currentType: null,
    showTypeDialog: false,
    currentTypeIndex: 3,
    currentImgUrl: '',
    showCamera: true,
  },

  protected: {
    category: 'certificates',
  },

  computed: {
    typeList() {
      return fileTypeList.filter(it => it.category === this.category)
    },
    showMask() {
      return ['id'].includes(this.category)
    },
    // currentType() {
    //   return this.typeList ? this.typeList[this.currentTypeIndex] : null
    // },
  },

  onInit() {
    storage.get(KEY_CAMERA).then(res => {
      this.showNewDialog = !res
    })
  },

  onReady() {
    this.setCanvasStyle()

    // setTimeout(() => {
    //   this.currentType = this.typeList[this.currentTypeIndex]
    // }, 30)
  },

  onShow() {
    if (adsdk.provider() === 'huawei') {
      this.$child('permissionDialog')?.show()
    }
    this.showCamera = true
    if (this.typeList.length === 1) {
      this.showTypeDialog = false
      this.currentType = this.typeList[0]
    } else {
      this.showTypeDialog = true

      this.currentType = this.typeList[this.currentTypeIndex]
        ? this.typeList[this.currentTypeIndex]
        : this.typeList[0]
    }
  },

  setCanvasStyle() {
    const mask = this.$element('mask')
    const canvasContext = mask.getContext('2d')
    const { x, y, w, h, containerH } = this.getFillSize()
    canvasContext.fillStyle = '#aa000000'
    canvasContext.fillRect(0, 0, 750, containerH)
    // 设置组合效果
    canvasContext.globalCompositeOperation = 'destination-out'
    canvasContext.fillRect(x, y, w, h)
  },

  getFillSize() {
    const pageH = this.$page.windowHeight * (750 / this.$page.windowWidth)
    const containerH = pageH - 88 - 88 - 200
    const w = 500
    const h = 300
    const x = (750 - w) / 2
    const y = (containerH - h) / 2

    return { w, h, x, y, containerH }
  },

  /**
   * 裁切图片：这里的尺寸都是图片像素点的尺寸
   * @param uri
   * @param x
   * @param y
   * @param width
   * @param height
   * @returns {Promise<unknown>}
   */
  imgCrop(uri, x, y, width, height) {
    return new Promise((resolve, reject) => {
      image.applyOperations({
        uri: uri,
        operations: [
          {
            action: 'crop',
            x,
            y,
            width,
            height,
          },
        ],
        quality: 90,
        success: function (data) {
          resolve(data)
        },
        fail: function (data, code) {
          reject({ data, code })
        },
      })
    })
  },

  handleTakePhoto() {
    this.takePhoto()
      .then(({ uri }) => this.getImageInfo(uri))
      .then(({ uri, height }) => {
        // 裁切图片逻辑暂时移除
        // if (this.showMask) {
        //   const { w, h, x, y, containerH } = this.getFillSize()
        //   const scale = containerH / height
        //
        //   return this.imgCrop(uri, x / scale, y / scale, w / scale, h / scale)
        // }
        return { uri }
      })
      .then(({ uri }) => this.toImagePreview(uri))
      .catch(e => {
        showToast('拍照失败')
      })
  },

  getImageInfo(uri) {
    return new Promise((resolve, reject) => {
      image.getImageInfo({
        uri: uri,
        success(data) {
          resolve(data)
        },
        fail(err) {
          reject(err)
        },
      })
    })
  },

  toImagePreview(uri) {
    router.push({
      uri: 'ocr/ImgPreview',
      params: {
        uri,
        certificatesType: this.currentType,
      },
    })
    // 在oppo 上，相机后置摄像头一次只能拍一张照片；必须要先销毁
    this.showCamera = false
  },

  takePhoto() {
    return new Promise((resolve, reject) => {
      this.$element('camera').takePhoto({
        quality: 'high',
        success: data => {
          resolve(data)
        },
        fail(err) {
          reject(err)
        },
      })
    })
  },

  // 权限不允许处理
  handlerError() {
    showToast('用户不允许使用摄像头')
    this.$child('permissionDialog')?.hide()
  },

  handleCameraInitDone() {
    this.$child('permissionDialog')?.hide()
  },

  handleClickType(item, index) {
    this.currentTypeIndex = index
    this.currentType = item
  },

  takePhoneSoon() {
    if (!this.currentType) {
      showToast('请先选择类型')
      return
    }
    this.showTypeDialog = false
  },

  back() {
    router.back()
  },

  closeNewDialog() {
    this.showNewDialog = false
    storage.set(KEY_CAMERA, true)
  },

  pickImage() {
    this.$child('permissionDialog')?.show()
    media.pickImage({
      success: data => {
        this.toImagePreview(data.uri)
        console.log(`pickImage handling success: ${data.uri}`)
      },
      fail: (data, code) => {
        showToast('您已拒绝访问相册')
        console.log(`pickImage handling fail, code = ${code}`)
      },
      complete: () => {
        this.$child('permissionDialog')?.hide()
      },
    })
  },
}
</script>

<style lang="less">
.page {
  flex-direction: column;
  height: 100%;
  background-color: #f0f2f5;
}

.header {
  height: 88px;
  margin-top: 88px;
  align-items: center;
  justify-content: center;
  width: 100%;

  &__title {
    font-size: 36px;
    font-weight: bolder;
    color: #333333;
    line-height: 36px;
  }

  &__back-btn {
    position: absolute;
    left: 40px;
    top: 16px;
    width: 56px;
    height: 56px;
    background-color: #ffffff;
    border-radius: 20px;
    align-items: center;
    justify-content: center;

    image {
      width: 30px;
      height: 30px;
    }
  }
}

.camera-box {
  flex: 1;
  .camera-com {
    width: 100%;
    height: 100%;
  }

  .mask-box {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
}

.footer {
  height: 200px;
  align-items: center;
  justify-content: center;
  background-color: white;

  &__pick-img {
    position: absolute;
    left: 40px;
    width: 100px;
  }
}

.dialog {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
}

.new-dialog {
  &__inner {
    flex-direction: column;
    align-items: center;
    width: 610px;
    height: 980px;
    background: linear-gradient(180deg, #4d82e7 0%, #ffffff 100%);
    border-radius: 24px;

    &__pic {
      width: 570px;
      height: 600px;
      border-radius: 20px;
      margin-top: 20px;
    }

    &__title {
      font-size: 36px;
      font-weight: bolder;
      color: #333333;
      margin-top: 60px;
    }

    &__tip {
      font-size: 28px;
      color: #333333;
      line-height: 40px;
      margin-top: 16px;
    }

    &__btn {
      width: 518px;
      height: 88px;
      background-color: #4e8cff;
      border-radius: 44px;
      font-size: 32px;
      font-weight: bolder;
      color: #ffffff;
      margin-top: 60px;
    }
  }
}

.type-dialog {
  &__inner {
    flex-direction: column;
    align-items: center;
    width: 610px;
    background-color: white;
    border-radius: 24px;
    padding: 24px;

    &__title {
      align-self: flex-start;
      font-weight: bolder;
      font-size: 36px;
      color: black;
    }

    &__list {
      flex-direction: column;

      &__item {
        width: 550px;
        height: 174px;
        background-color: #ffffff;
        border-radius: 19px;
        border: 1px solid rgba(142, 175, 216, 0.3);
        align-items: center;
        margin-top: 20px;
        background-image: url('/ocr/assets/list_item_bg.webp');
        background-size: cover;

        &--active {
          border: 1px solid #000;
        }

        &__bg {
          position: absolute;
          left: 2px;
          top: 2px;
          width: 260px;
          height: 169px;
          background: linear-gradient(270deg, #f1f7ff 0%, #d2e5ff 100%);
          box-shadow: inset -8px 0px 32px 0px #a5bdf9;
          border-top-left-radius: 20px;
          border-bottom-left-radius: 20px;
        }

        &__icon {
          width: 160px;
          margin-left: 34px;
        }

        &__info {
          flex-direction: column;
          margin-left: 80px;

          &__title {
            font-size: 36px;
            font-weight: bolder;
            color: #333333;
            line-height: 36px;
          }

          &__desc {
            font-size: 24px;
            color: rgba(51, 51, 51, 0.6);
            line-height: 24px;
            margin-top: 12px;
          }
        }
      }
    }

    &__btn {
      width: 518px;
      height: 88px;
      background-color: #4e8cff;
      border-radius: 44px;
      font-size: 32px;
      font-weight: bolder;
      color: #ffffff;
      margin-top: 60px;
    }
  }
}
</style>
