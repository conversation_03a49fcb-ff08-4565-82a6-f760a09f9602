<import name="apex-spin" src="@quickapp/apex-ui/components/spin/index"></import>
<import
  name="nativead"
  src="@quickapp/business/components/nativead.ux"
></import>

<template>
  <div class="wrapper">
    <div class="list">
      <div class="list__item" @click="toCamera('text')">
        <div class="list__item__text">
          <text class="list__item__title">文件扫描</text>
          <text class="list__item__desc">纸质文件转PDF扫描件</text>
        </div>
        <image
          class="list__item__icon"
          src="/ocr/assets/ill/ill_scanning.webp"
        ></image>
      </div>
      <div class="list__item" @click="toCamera('id')">
        <div class="list__item__text">
          <text class="list__item__title">身份证扫描</text>
          <text class="list__item__desc">精准识别图片文字</text>
        </div>
        <image
          class="list__item__icon"
          src="/ocr/assets/ill/ill_id.webp"
        ></image>
      </div>
      <div class="list__item list__item--half" @click="toCamera('table')">
        <div class="list__item__text">
          <text class="list__item__title">表格识别</text>
          <text class="list__item__desc">直接生成EXCEL文件</text>
        </div>
        <image
          class="list__item__icon"
          src="/ocr/assets/ill/ill_table.webp"
        ></image>
      </div>
      <div class="list__item list__item--half" @click="toCamera('text')">
        <div class="list__item__text">
          <text class="list__item__title">文字识别</text>
          <text class="list__item__desc">精准识别图片文字</text>
        </div>
        <image
          class="list__item__icon"
          src="/ocr/assets/ill/ill_text.webp"
        ></image>
      </div>
      <div
        class="list__item list__item--half"
        @click="toCamera('certificates')"
      >
        <div class="list__item__text">
          <text class="list__item__title">证件扫描</text>
          <text class="list__item__desc">直接生成PDF文件</text>
        </div>
        <image
          class="list__item__icon"
          src="/ocr/assets/ill/ill_certificates.webp"
        ></image>
      </div>
    </div>
    <div class="ad-box">
      <nativead
        oppo-id="2152553"
        honor-id="1910595381898903552"
        huawei-id="t525t6rjcp"
      ></nativead>
    </div>
    <div if="loading" class="loading-wrap">
      <apex-spin loading="{{loading}}" tip="正在解析..."></apex-spin>
    </div>
  </div>
</template>

<script>
import media from '@system.media'
import file from '@system.file'
import { storage } from '@quickapp/utils'
import image from '@system.image'
import dayjs from 'dayjs'
import { filePath, fileTypeList } from './config/files'
import router from '@system.router'
import request from '@system.request'
import { getUserInfo } from '@quickapp/utils/lib/api/ocr/user'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    // 修改 lifeCycleShow 的值，执行 handleLifeCycleShow
    lifeCycleShow: {
      type: Number,
    },
  },

  data() {
    return {
      loading: false,
      list: [...fileTypeList],
      currentType: {},
      userInfo: {},
    }
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
  },

  onInit() {
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('lifeCycleShow', 'handleLifeCycleShow')

    // this.toCamera()
    // router.push({
    //   uri: 'ocr/Recharge',
    // })
  },

  handleLifeCycleShow() {
    this.getUserInfo()
  },

  toCamera(category) {
    router.push({
      uri: 'ocr/Camera',
      params: {
        category,
      },
    })
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getUserInfo()
    }
  },

  getUserInfo() {
    getUserInfo().then(res => {
      if (res) {
        this.userInfo = res
      }
    })
  },

  showLoading() {
    this.loading = true
  },

  hideLoading() {
    this.loading = false
  },

  async takePhoto(item) {
    this.currentType = item

    if (!item.type) {
      return router.push({ uri: 'ocr/MyDoc' })
    }

    const hasTakePhoto = await this.getLocalFirstTakePhoto()

    if (!hasTakePhoto) {
      this.setLocalFirstTakePhoto()
      this.handleTakePhoto()
      return
    }

    this.handleTakePhoto()
  },

  setLocalFirstTakePhoto() {
    return storage.set('isFirstTakePhoto', true)
  },

  getLocalFirstTakePhoto() {
    return storage.get('isFirstTakePhoto')
  },

  async handleTakePhoto() {
    console.log('------handleTakePhoto')
    return (
      this.mediaTakePhoto()
        .then(({ uri }) => this.compressImage(uri))
        .then(data => {
          this.showLoading()
          return data
        })
        // .then(data => this.requestUpload(data.uri, this.currentType.type))
        // .then(res => (res.data ? res.data.join('\n') : '服务器解析错误'))
        // .then(text => this.writeText(text, this.currentType.id))
        // .then(({ uri }) => {
        //   router.push({
        //     uri: 'ocr/PreViewDoc',
        //     params: {
        //       fileUri: uri,
        //     },
        //   })
        // })
        // .catch(() => showToast('解析失败，请重试'))
        .finally(this.hideLoading.bind(this))
    )
  },

  // 拍照
  mediaTakePhoto() {
    return new Promise((resolve, reject) => {
      media.takePhoto({
        success: resolve,
        fail: () => reject('您可能未开权限'),
      })
    })
  },

  // 压缩图片
  compressImage(uri) {
    return new Promise((resolve, reject) => {
      image.compressImage({
        uri,
        quality: 5,
        success: data => {
          resolve(data)
        },
        fail: () => reject('网络错误'),
      })
    })
  },

  // 写入文件
  writeText(text, id) {
    let extension = '.doc'
    const fileName = dayjs().format('YYYY年MM月DD日HH点mm分ss秒') + '.' + id
    const uri = filePath + fileName + extension

    return new Promise((resolve, reject) => {
      file.writeText({
        uri: uri,
        text: text,
        success: () => {
          resolve({ uri })
        },
        fail: (data, code) => {
          reject('解析失败')
        },
      })
    })
  },

  // 文件上传
  requestUpload(uri, type) {
    return request({
      isUpload: true,
      files: [{ uri: uri }],
      data: {
        type: type,
      },
    })
  },
  handleOss(fileData) {
    console.log(fileData)
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 148px;
  overflow: auto;
  background-color: #18222f;
}

.list {
  flex: 1;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 28px;

  &__item {
    width: 100%;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 240px;
    background-color: white;
    padding: 32px;
    border-radius: 20px;
    margin-bottom: 24px;

    &__text {
      height: 100%;
      align-items: flex-start;
      justify-content: center;
      flex-direction: column;
    }

    &--half {
      width: 336px;
    }

    &__icon {
      width: 130px;
      height: 130px;
    }

    &__title {
      font-size: 36px;
      font-weight: bolder;
      color: #333;
      margin-top: 6px;
    }

    &__desc {
      font-size: 24px;
      color: #333;
      margin-top: 12px;
    }
  }
}

.ad-box {
  width: 100%;
  flex-direction: column;
}

.loading-wrap {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5);
}
</style>
