<template>
  <div class="wrapper">
    <div class="shopping-list">
      <div
        class="shopping-item-wrapper"
        for="item in items"
        style="{{ $idx % 2 === 0 ? 'width: 390px;' : 'width: 248px;' }}"
      >
        <image src="{{item.image}}" @click="onItemClick(item)"></image>
      </div>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import list from '@/flower/list'

export default {
  props: {
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },
  data: {
    items: list,
  },
  onInit() {
    this.getInitData()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  onReady() {},

  onDestroy() {},

  onItemClick(item) {
    router.push({
      uri: 'flower/detail',
      params: item,
    })
  },

  handleStorageChange() {
    this.getInitData()
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      this.getInitData()
    }
  },
  async getInitData() {},
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fefefe;
}

.shopping-list {
  flex: 1;
  padding: 35px;
  margin-top: 80px;
  flex-direction: row;
  flex-wrap: wrap;
}

.shopping-item-wrapper {
  margin: 10px;

  image {
    width: 100%;
    height: 384px;
  }
}
</style>
