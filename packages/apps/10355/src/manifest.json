{"id": "10355", "package": "ai.lingjieyu.textv", "configCode": "cf_textling", "name": "零界语", "versionName": "1.0.6", "versionCode": 6, "minPlatformVersion": 1080, "privacyUrl": "https://landing.serveclouds.com/privacy/10355/PrivacyPolicy.html", "userUrl": "https://landing.serveclouds.com/privacy/10355/UserPolicy.html", "icon": "/assets/images/logo.png", "features": [{"name": "system.prompt"}, {"name": "system.router"}, {"name": "system.canvas"}, {"name": "system.shortcut"}, {"name": "system.fetch"}, {"name": "system.geolocation"}, {"name": "system.device"}, {"name": "system.audio"}, {"name": "system.image"}, {"name": "system.network"}, {"name": "system.webview"}, {"name": "system.request"}, {"name": "system.package"}, {"name": "system.storage"}, {"name": "service.account"}, {"name": "system.file"}, {"name": "service.ad"}, {"name": "system.nfc"}, {"name": "system.clipboard"}, {"name": "system.sensor"}], "permissions": [{"origin": "*"}], "config": {"logLevel": "debug", "requestNotificationPermission": false}, "router": {"entry": "pages/Flash", "pages": {"pages/Home": {"component": "index"}, "pages/Flash": {"component": "index"}, "pages/Splash": {"launchMode": "singleTask", "component": "index"}, "clean/cleaning": {"component": "index"}, "weather/City": {"component": "index"}, "billing/detail": {"component": "index"}, "flower/detail": {"component": "index"}, "shopping/detail": {"component": "index"}, "ocr/MyDoc": {"component": "index"}, "ocr/ImgPreview": {"component": "index"}, "ocr/PreViewDoc": {"component": "index"}, "ocr/Camera": {"component": "index"}, "ocr/CertificatesList": {"component": "index"}, "luck/ToDo": {"component": "index"}}}, "display": {"titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141", "themeMode": 0, "menuBarData": {"menuBar": false}, "pages": {"pages/Home": {"titleBarText": "首页", "titleBar": false, "themeMode": 0, "menuBarData": {"menuBar": false}, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0}, "pages/Flash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Splash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "fitCutout": "portrait", "fullScreen": true, "menu": true}}}}