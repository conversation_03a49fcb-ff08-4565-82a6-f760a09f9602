<import name="my-header" src="./components/my-header.ux"></import>
<import name="my-two-days" src="./components/my-two-days.ux"></import>
<import
  name="my-temperature-list"
  src="./components/my-temperature-list.ux"
></import>
<import name="my-other-info" src="./components/my-other-info.ux"></import>
<import
  name="nativead"
  src="@quickapp/business/components/nativead.ux"
></import>
<import name="short-cut" src="@quickapp/mc-ui/components/short-cut.ux"></import>

<template>
  <div class="wrapper">
    <my-header
      location-info="{{locationInfo}}"
      weather-now="{{weatherNow}}"
      air-now="{{airNow}}"
    ></my-header>
    <div class="content">
      <div>
        <my-two-days
          hour-rain-list="{{hourRainList}}"
          weather-daily="{{twoDailyWeather}}"
        ></my-two-days>
      </div>
      <div class="block">
        <nativead adid="native_ad"></nativead>
      </div>
      <div if="{{weatherHourly && weatherHourly.length}}" class="block">
        <my-temperature-list
          swiper-height="360px"
          list="{{weatherHourly}}"
        ></my-temperature-list>
      </div>
      <div class="block">
        <nativead adid="native_ad2"></nativead>
      </div>
      <div if="{{weatherDaily && weatherDaily.length}}" class="block">
        <my-temperature-list
          title="15日天气预报"
          type="15days"
          swiper-height="560px"
          list="{{weatherDaily}}"
        ></my-temperature-list>
      </div>
      <div class="block">
        <nativead honor-id="1915339256613699584"></nativead>
      </div>
      <div class="block">
        <my-other-info info="{{otherInfo}}"></my-other-info>
      </div>
    </div>
    <!--<input-->
    <!--  type='button'-->
    <!--  if="{{showShortcutBtn && index === currentIndex}}"-->
    <!--  class="add-desk"-->
    <!--  value="无需下载，添加至桌面"-->
    <!--  @click="addDesk"-->
    <!--&gt;</input>-->
    <!--<div if="{{ index === currentIndex}}" class="add-desk">-->
    <!--  <short-cut width="100%" height="100%" bg-color="#916EFF"></short-cut>-->
    <!--</div>-->

    <div class="protocol-box">
      <text class="btn" @click="toPrivacyProtocol">隐私协议</text>
      <text class="btn" @click="toUserProtocol">用户协议</text>
    </div>
    <!-- 占位符 -->
    <div if="{{showShortcutBtn}}" style="height: 156px"></div>
  </div>
</template>

<script>
import weather from '@/weather/api/weather'
import utils from '@/weather/helper/utils'
import { showToast, storage, toPrivacyPage, toUserPage } from '@quickapp/utils'
import router from '@system.router'
import shortcut from '@system.shortcut'
import adsdk from '@quickapp/business/lib/adsdk'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    lifeCycleShow: {
      type: Number,
    },
  },

  data: {
    todayList: [],
    locationInfo: {
      code: 'c1101',
      street: '北京市',
      district: '北京市',
      city: '北京市',
      province: '北京市',
      level: 2,
    },
    weatherNow: {
      clouds: '0',
      code: '1',
      dew_point: '',
      feels_like: '27',
      humidity: '81',
      pressure: '1000',
      temperature: '24',
      text: '晴',
      time: null,
      visibility: '7.0',
      wind_direction: '西南',
      wind_direction_degree: '210',
      wind_scale: '1',
      wind_speed: '5.0',
      audioUrl: '',
    },
    airNow: {
      aqi: '24',
      co: '0.542',
      last_update: '2022-07-20T22:00:00+08:00',
      no2: '19',
      o3: '42',
      pm10: '24',
      pm25: '12',
      primary_pollutant: '',
      quality: '优',
      so2: '6',
    },
    hourRainList: [
      '2022-07-21T10:00:00+08:00',
      '2022-08-21T10:00:00+08:00',
      '2022-09-21T10:00:00+08:00',
    ],
    weatherDaily: [
      // {
      //   code_day: '13',
      //   code_night: '9',
      //   date: '2022-07-20',
      //   high: '37',
      //   humidity: '88',
      //   low: '27',
      //   precip: '0.58',
      //   rainfall: '5.40',
      //   text_day: '小雨',
      //   text_night: '阴',
      //   wind_direction: '西南',
      //   wind_direction_degree: '225',
      //   wind_scale: '4',
      //   wind_speed: '23.4',
      // },
      // {
      //   code_day: '13',
      //   code_night: '9',
      //   date: '2022-07-20',
      //   high: '37',
      //   humidity: '88',
      //   low: '27',
      //   precip: '0.58',
      //   rainfall: '5.40',
      //   text_day: '小雨',
      //   text_night: '阴',
      //   wind_direction: '西南',
      //   wind_direction_degree: '225',
      //   wind_scale: '4',
      //   wind_speed: '23.4',
      // },
    ],
    twoDailyWeather: [
      {
        code_day: '13',
        code_night: '9',
        date: '2022-07-20',
        high: '37',
        humidity: '88',
        low: '27',
        precip: '0.58',
        rainfall: '5.40',
        text_day: '小雨',
        text_night: '阴',
        wind_direction: '西南',
        wind_direction_degree: '225',
        wind_scale: '4',
        wind_speed: '23.4',
      },
      {
        code_day: '13',
        code_night: '9',
        date: '2022-07-20',
        high: '37',
        humidity: '88',
        low: '27',
        precip: '0.58',
        rainfall: '5.40',
        text_day: '小雨',
        text_night: '阴',
        wind_direction: '西南',
        wind_direction_degree: '225',
        wind_scale: '4',
        wind_speed: '23.4',
      },
    ],
    weatherHourly: [],
    otherInfo: {},
    showShortcutBtn: true,
  },

  onInit() {
    // 执行逻辑
    // this.getInitData()
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('lifeCycleShow', 'handleLifeCycleShow')
  },

  onDestroy() {},

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getInitData()
    }

    this.shortcutHasInstall()
  },

  handleLifeCycleShow() {
    this.getInitData()
  },

  shortcutHasInstall() {
    shortcut.hasInstalled({
      success: result => {
        this.showShortcutBtn = !result
      },
    })
  },

  getInitData() {
    utils
      .getCityInfo()
      .then(cityInfo => {
        this.locationInfo = cityInfo

        const province = cityInfo.province
        const city = cityInfo.city
        const district = cityInfo.district
        const street = cityInfo.street
        const latitude = cityInfo.latitude
        const longitude = cityInfo.longitude

        const params = [province, city, city, city, latitude, longitude]

        weather.weatherViewNow(...params).then(res => {
          this.handleWeatherViewNowRes(res)
        })

        weather.rainState(...params).then(res => {
          console.log('res rainState 111====>', res)
          this.hourRainList = res.hour_rain_list
        })
      })
      .catch(err => {
        router.push({
          uri: 'weather/City',
        })
      })
  },

  handleWeatherViewNowRes(res) {
    console.log('res weatherViewNow ====>', res)
    this.weatherNow = Object.assign(
      {
        audioUrl:
          res.speech_audio && res.speech_audio.audio_url
            ? res.speech_audio.audio_url
            : '',
      },
      res.weather_now
    )

    console.log('this.weatherNow', this.weatherNow)

    this.airNow = res.air_now

    this.otherInfo = {
      sunrise: res.sun_riseset ? res.sun_riseset.sunrise : '', // 日出
      sunset: res.sun_riseset ? res.sun_riseset.sunset : '', // 日落
      feelsLike: res.weather_now ? res.weather_now.feels_like : '', // 体感
      wind: res.weather_now ? res.weather_now.wind_direction : '', // 风向
      water: res.weather_now ? res.weather_now.humidity : '', // 湿度
      seeing: res.weather_now ? res.weather_now.visibility : '', // 能见度
      level: res.weather_now ? res.weather_now.wind_scale : '', // 等级
    }

    const date = new Date()
    const y = date.getFullYear()
    const m = date.getMonth() + 1
    const d = date.getDate()
    const addZero = n => (n < 10 ? `0${n}` : n)
    const today = y + '-' + addZero(m) + '-' + addZero(d)

    const todayIndex = res.weather_daily.findIndex(it => it.date === today)
    const start = todayIndex === -1 ? 0 : todayIndex
    const end = start + 2
    this.twoDailyWeather = res.weather_daily.slice(start, end)

    this.weatherHourly = res.weather_hourly.map(it => {
      const nowDate = new Date()
      const apiDate = new Date(it.time)
      const hour = apiDate.getHours()
      const day = apiDate.getDay()
      const nowHour = nowDate.getHours()
      const nowDay = nowDate.getDay()
      const time = hour === nowHour ? '现在' : hour + '时'
      const disabled = hour < nowHour && day <= nowDay

      return {
        time: time,
        temperature: it.temperature, // 当天温度
        // minTemperature: 11, // 最小温度
        // maxTemperature: 20, // 最大温度
        weather: 'sunny',
        wind: it.wind_direction + '风',
        level: it.wind_speed,
        disabled: disabled,
        iconCode: it.code,
      }
    })

    console.log('res.weather_hourly', res.weather_hourly)

    this.weatherDaily = res.weather_daily.map(it => {
      const date = new Date()
      const nowYear = date.getFullYear()
      const nowMonth = date.getMonth() + 1
      const nowD = date.getDate()

      const nowTime = Number(
        [nowYear, addZero(nowMonth), addZero(nowD)].join('')
      )

      const apiTime = Number(it.date.split('-').join(''))
      let time = ''
      const disabled = apiTime < nowTime
      const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      switch (apiTime - nowTime) {
        case -1:
          time = '昨天'
          break
        case 0:
          time = '现在'
          break
        case 1:
          time = '明天'
          break
        case 2:
          time = '后天'
          break
        default:
          time = weeks[new Date(it.date).getDay()]
      }

      return {
        time: time,
        // temperature: it.temperature, // 当天温度
        minTemperature: Number(it.low), // 最小温度
        maxTemperature: Number(it.high), // 最大温度
        weather: 'sunny',
        wind:
          it.wind_direction === '无持续风'
            ? it.wind_direction
            : it.wind_direction + '风',
        level: it.wind_speed,
        disabled: disabled,
        iconCode: it.code_day,
      }
    })
  },

  toUserProtocol() {
    toUserPage()
  },

  toPrivacyProtocol() {
    toPrivacyPage()
  },

  addDesk() {
    // if (event.eventStatusCode === 200) {
    //   this.showShortcutBtn = false
    // } else {
    //   this.showShortcutBtn = true
    // }

    shortcut.install({
      success: () => {
        this.showShortcutBtn = false
      },
      fail: () => {
        showToast('您的手机不支持添加到桌面功能')
      },
    })
  },
}
</script>

<style lang="less">
@import './assets/styles/style';

.wrapper {
  flex-direction: column;
  /* padding-bottom: 156px; */
}

.content {
  flex-direction: column;
  padding: 0 20px;
  margin: -60px auto 0;
  z-index: 2;

  .block {
    width: 100%;
    margin-top: 20px;
  }
}

.add-desk {
  position: fixed;
  bottom: 176px;
  left: 94px;
  width: 570px;
  height: 80px;
  //background-color: @mainColor;
  //border-radius: 44px;
  //margin: 44px auto 0;
  //font-size: 32px;
  //font-weight: bolder;
  //color: #ffffff;
}

.protocol-box {
  width: 500px;
  margin: 0 auto;
  .btn {
    flex: 1;
    text-align: center;
    background-color: rgba(0, 0, 0, 0);
    height: 100px;
    color: blue;
  }
}

.dialog-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 99;

  .dialog {
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 560px;
    /* height: 850px; */
    padding: 50px 50px 40px;
    background-color: #ffffff;
    border-radius: 32px;

    .title {
      font-size: 36px;
      font-weight: bolder;
      color: #111111;
      line-height: 44px;
    }

    .content {
      flex: 1;
      flex-direction: column;
      margin: 26px 0;
    }

    .footer {
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;

      .agree {
        width: 460px;
        height: 88px;
        background-color: #677dff;
        border-radius: 44px;
        font-size: 32px;
        font-weight: bolder;
        color: #ffffff;
      }

      .reject {
        font-size: 28px;
        color: #999999;
        margin-top: 24px;
      }
    }
  }
}
</style>
