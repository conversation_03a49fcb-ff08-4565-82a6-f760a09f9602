<template>
  <!-- style="{{width: width, height: height}}"-->
  <image
    class="icon"
    src="{{imgSrcMap[status] ? imgSrcMap[status] : imgSrcMap['0']}}"
    style="width: {{width}}; height: {{height}}"
  ></image>
</template>

<script>
const imgSrcMap = {
  0: '/weather/assets/icon-weather/icon_weather_code_0.png',
  1: '/weather/assets/icon-weather/icon_weather_code_1.png',
  4: '/weather/assets/icon-weather/icon_weather_code_4.png',
  6: '/weather/assets/icon-weather/icon_weather_code_6.png',
  9: '/weather/assets/icon-weather/icon_weather_code_9.png',
  10: '/weather/assets/icon-weather/icon_weather_code_10.png',
  11: '/weather/assets/icon-weather/icon_weather_code_11.png',
  12: '/weather/assets/icon-weather/icon_weather_code_12.png',
  13: '/weather/assets/icon-weather/icon_weather_code_13.png',
  14: '/weather/assets/icon-weather/icon_weather_code_14.png',
  15: '/weather/assets/icon-weather/icon_weather_code_15.png',
  16: '/weather/assets/icon-weather/icon_weather_code_16.png',
  17: '/weather/assets/icon-weather/icon_weather_code_17.png',
  18: '/weather/assets/icon-weather/icon_weather_code_18.png',
  19: '/weather/assets/icon-weather/icon_weather_code_19.png',
  20: '/weather/assets/icon-weather/icon_weather_code_20.png',
  21: '/weather/assets/icon-weather/icon_weather_code_21.png',
  22: '/weather/assets/icon-weather/icon_weather_code_22.png',
  23: '/weather/assets/icon-weather/icon_weather_code_23.png',
  24: '/weather/assets/icon-weather/icon_weather_code_24.png',
  25: '/weather/assets/icon-weather/icon_weather_code_25.png',
  26: '/weather/assets/icon-weather/icon_weather_code_26.png',
  28: '/weather/assets/icon-weather/icon_weather_code_28.png',
  30: '/weather/assets/icon-weather/icon_weather_code_30.png',
  31: '/weather/assets/icon-weather/icon_weather_code_31.png',
  33: '/weather/assets/icon-weather/icon_weather_code_33.png',
  36: '/weather/assets/icon-weather/icon_weather_code_36.png',
  37: '/weather/assets/icon-weather/icon_weather_code_37.png',
  38: '/weather/assets/icon-weather/icon_weather_code_38.png',
  99: '/weather/assets/icon-weather/icon_weather_code_0.png',
}

export default {
  data: {
    // 后面根据 api 可以写更多的映射
    imgSrcMap: imgSrcMap,
  },

  props: {
    status: {
      type: String,
      default: '25',
      validator: value => {
        // 这个值必须匹配下列字符串中的一个
        return Object.keys(imgSrcMap).indexOf(value) !== -1
      },
    },
    width: {
      type: String,
      default: '56px',
    },
    height: {
      type: String,
      default: '56px',
    },
  },
}
</script>
