<import name="my-search" src="../components/my-search.ux"></import>

<template>
  <div class="wrapper">
    <div class="search-box">
      <my-search
        value="{{searchText}}"
        @change="handleChange"
        @search="areaSearch"
      ></my-search>
    </div>
    <div if="{{searchText}}" class="list-box">
      <div if="{{!searchList || searchList.length <= 0}}" class="no-data">
        <text>暂无数据</text>
      </div>
      <text
        class="item"
        for="item in searchList"
        tid="$idx"
        @click="setLocation(item)"
      >
        {{
          item.province +
          ' ' +
          item.city +
          ' ' +
          item.district +
          ' ' +
          item.street
        }}
      </text>
    </div>
    <div else class="hot-city">
      <!--<div class="header">-->
      <!--  <image class="icon" src="/assets/images/ic_adress_grey.png"></image>-->
      <!--  <text class="text">当前位置：</text>-->
      <!--  <text if="{{currentCity}}" class="current-city">-->
      <!--    {{ currentCity.city }}-->
      <!--  </text>-->
      <!--  <text else class="btn" @click="handlePosition">立即定位</text>-->
      <!--</div>-->
      <div class="hot-list-box">
        <text
          class="item"
          for="item in hotCityList"
          tid="$idx"
          @click="setLocation(item)"
        >
          {{ item.city }}
        </text>
      </div>
    </div>
  </div>
</template>

<script>
import utils from '../helper/utils'
import weather from '../api/weather'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    searchText: '',
    hotCityList: [],
    searchList: [],
    selectCity: null,
    currentCity: null,
  },

  onInit() {
    weather.areaRecommend().then(res => {
      this.hotCityList = res.hot_city
      utils.getLocationCityInfo().then(res => {
        if (!res) {
          this.selectCity = this.hotCityList[0]
          utils.setLocationCityInfo(this.hotCityList[0])
        } else {
          this.selectCity = res
        }
      })
    })

    // utils.getUserRejectPosition().then(userHasReject => {
    //   if (!userHasReject) {
    //     this.getPostionInfo()
    //   }
    // })
  },

  handleChange(e) {
    this.searchText = e.detail.data

    if (this.searchText) {
      this.areaSearch()
      setTimeout(() => {
        this.areaSearch()
      }, 300)
    }
  },

  getPostionInfo(callback) {
    utils
      .getPostionInfo()
      .then(res => {
        this.currentCity = res
        callback && callback(res)
      })
      .catch(err => {
        console.log(err)
        utils.showToast('定位失败，请打开GPS定位')
        this.currentCity = null
      })
  },

  areaSearch() {
    weather.areaSearch(this.searchText).then(res => {
      console.log('搜索城市....', res)
      this.searchList = res
    })
  },

  // onBackPress() {
  //   return this.currentCity || this.selectCity
  // },

  handlePosition() {
    utils.removeUserRejectPosition().then(() => {
      this.getPostionInfo(res => {
        this.setLocation(res)
      })
    })
  },

  setLocation(item) {
    // this.selectCity = item
    utils
      .setLocationCityInfo(item)
      .then(() => {
        this.selectCity = item
        this.$page.finish()
      })
      .catch(() => {
        this.selectCity = null
      })
  },
})
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  padding-top: 88px;
  background-color: #f2f4f7;
}

.search-box {
  position: fixed;
  top: 0;
}

.list-box {
  flex-direction: column;
  background-color: #fff;
  padding: 0 32px;

  .item {
    height: 90px;
    line-height: 45px;
    border-bottom: 1px solid #eee;
  }
}

.hot-city {
  flex-direction: column;
  width: 686px;
  background-color: #ffffff;
  margin: 20px auto;
  padding: 48px 0 32px 32px;
  border-radius: 24px;

  .header {
    align-items: center;

    .icon {
      width: 36px;
      height: 36px;
    }

    .text {
      font-size: 28px;
      font-weight: 500;
      color: #333333;
      line-height: 36px;
    }

    .current-city {
      font-size: 28px;
    }

    .btn {
      text-align: center;
      width: 128px;
      height: 48px;
      border-radius: 24px;
      border: 1px solid #677dff;
      font-size: 24px;
      font-weight: 500;
      color: #677dff;
    }
  }

  .hot-list-box {
    flex-wrap: wrap;
    padding-top: 24px;

    .item {
      width: 198px;
      height: 78px;
      margin-right: 14px;
      margin-bottom: 14px;
      justify-content: center;
      text-align: center;
      background-color: #f2f3f4;
      border-radius: 8px;
      font-size: 28px;
    }
  }
}

.no-data {
  height: 300px;
  justify-content: center;
  align-items: center;
}
</style>
