<template>
  <div class="wrapper">
    <image src="{{image}}"></image>
    <div class="tip">
      <text class="tip__text1">超值特惠</text>
      <text class="tip__text2">领券购买享超低折扣！</text>
    </div>
    <text class="name">{{ name }}</text>
    <text class="price">{{ price }}</text>
    <div style="flex: 1"></div>
    <div class="button" @click="onButtonClick">
      <text>领券购买</text>
    </div>
  </div>
</template>

<script>
import clipboard from '@system.clipboard'
import { showToast } from '@quickapp/utils'
export default {
  data: {
    image: '',
    name: '',
    price: '',
  },
  onInit() {},

  onReady() {},

  onButtonClick() {
    clipboard.set({
      text: 'https://mobile.yangkeduo.com/goods2.html?ps=nsIBWGMaBm',
      success: function (data) {
        showToast('已复制链接，请在浏览器打开领券')
      },
      fail: function (data, code) {
        console.log(`handling fail, code = ${code}`)
      },
    })
  },

  onDestroy() {},
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;

  image {
    width: 750px;
    height: 760px;
  }

  .tip {
    width: 750px;
    height: 104px;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    background-color: #fe0000;

    &__text1 {
      font-weight: 600;
      font-size: 48px;
      color: #ffffff;
      line-height: 66px;
      text-align: left;
      font-style: normal;
    }

    &__text2 {
      font-weight: 600;
      font-size: 32px;
      color: #ffffff;
      line-height: 44px;
      text-align: left;
      font-style: normal;
    }
  }

  .name {
    font-weight: 600;
    font-size: 40px;
    color: #454545;
    line-height: 56px;
    text-align: left;
    font-style: normal;
    margin: 40px;
  }

  .price {
    font-weight: 600;
    font-size: 48px;
    color: #fe0000;
    line-height: 66px;
    text-align: left;
    font-style: normal;
    margin-left: 40px;
  }

  .button {
    width: 670px;
    height: 104px;
    align-self: center;
    justify-self: center;
    margin-bottom: 120px;
    background-color: #fe0000;
    border-radius: 52px;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    :active {
      background-color: #fe3333;
    }

    text {
      font-weight: 600;
      font-size: 48px;
      color: #ffffff;
      line-height: 66px;
      text-align: center;
      font-style: normal;
    }
  }
}
</style>
