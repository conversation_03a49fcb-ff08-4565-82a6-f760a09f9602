<template>
  <div class="wrapper">
    <div class="shopping-item" for="item in items" @click="onItemClick(item)">
      <image src="{{item.image}}"></image>
      <text class="name">{{ item.name }}</text>
      <text class="price">{{ item.price }}</text>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import list from '@/shopping/list'

export default {
  props: {
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },
  data: {
    items: list,
  },
  onInit() {
    this.getInitData()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  onReady() {},

  onDestroy() {},

  onItemClick(item) {
    router.push({
      uri: 'shopping/detail',
      params: item,
    })
  },

  handleStorageChange() {
    this.getInitData()
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      this.getInitData()
    }
  },
  async getInitData() {},
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  background-color: white;
  overflow: visible;
  padding: 60px 20px 20px 20px;

  .shopping-item {
    flex-direction: column;
    margin: 20px;

    image {
      width: 314px;
      height: 318px;
    }

    .name {
      width: 314px;
      font-weight: 600;
      font-size: 28px;
      color: #454545;
      line-height: 40px;
      text-align: left;
      font-style: normal;
      margin-top: 20px;
    }

    .price {
      font-weight: 600;
      font-size: 48px;
      color: #fe0000;
      line-height: 66px;
      text-align: left;
      font-style: normal;
      margin-top: 20px;
    }
  }
}
</style>
