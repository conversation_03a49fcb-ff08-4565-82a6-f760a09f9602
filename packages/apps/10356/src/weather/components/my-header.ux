<template>
  <div class="header">
    <div class="address" @click="toCityPage">
      <image class="ic-plus" src="/weather/assets/ic_plus.png"></image>
      <text class="tv-address">
        {{ locationInfo.district }} {{ locationInfo.street }}
      </text>
      <image class="ic-address" src="/weather/assets/ic_address.png"></image>
    </div>
    <!--不能0px;不能display:none-->
    <video
      style="width: 1px; height: 1px"
      id="video"
      @pause="handlePauseEvt"
      @start="handleStartEvt"
      @finish="handleFinisEvt"
      src="{{weatherNow.audioUrl}}"
    ></video>

    <div class="temperature">
      <div class="num-box">
        <text class="num">{{ weatherNow.temperature }}</text>
        <div class="unit"></div>
        <text class="status">{{ weatherNow.text }}</text>
        <div class="ic-voice-box" if="{{false}}">
          <image
            class="ic-voice"
            @click="handleVoice"
            src="/weather/assets/ic_voice.png"
          />
          <div if="{{showDot}}" class="dot"></div>
        </div>
      </div>
      <div class="status-box">
        <image class="icon" src="/weather/assets/leaves.png"></image>
        <text class="status-txt">{{ airNow.quality }} {{ airNow.aqi }}</text>
      </div>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { showToast } from '@quickapp/utils'
import ad from '@service.ad'

export default {
  props: {
    locationInfo: {
      type: Object,
      default: () => ({
        province: '',
        city: '',
        district: '',
        street: '',
      }),
    },
    weatherNow: {
      type: Object,
      default: () => ({
        clouds: '0',
        code: '1',
        dew_point: '',
        feels_like: '27',
        humidity: '81',
        pressure: '1000',
        temperature: '24',
        text: '晴',
        time: null,
        visibility: '7.0',
        wind_direction: '西南',
        wind_direction_degree: '210',
        wind_scale: '1',
        wind_speed: '5.0',
        audioUrl: '',
      }),
    },
    airNow: {
      type: Object,
      default: () => ({
        aqi: '24',
        co: '0.542',
        last_update: '2022-07-20T22:00:00+08:00',
        no2: '19',
        o3: '42',
        pm10: '24',
        pm25: '12',
        primary_pollutant: '',
        quality: '优',
        so2: '6',
      }),
    },
  },

  data() {
    return {
      adData: null,
      isVideStart: false,
      showDot: true,
      isUnlock: false,
    }
  },

  onInit() {
    const provider = ad.getProvider()
    console.log('provider', provider)
    this.loadAd()
  },

  loadAd() {},

  toCityPage() {
    router.push({
      uri: 'weather/City',
    })
  },

  async handleVoice() {
    if (this.isUnlock) {
      this.handleVoiceInternal()
      return
    }
    if (this.adData != null) {
      const brand = ad.getProvider()
      if (brand.toLowerCase() === 'oppo') {
        showToast('即将进入激励视频')
        await this.delay(2000)
      }
      this.handleVoiceInternal()
      this.isUnlock = true
      this.loadAd()
    } else {
      this.handleVoiceInternal()
      this.loadAd()
    }
  },

  delay(t = 1000) {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve()
      }, t)
    })
  },

  handleVoiceInternal() {
    const video = this.$element('video')
    // const video = this.$child('video')
    this.showDot = false
    if (this.isVideStart) {
      video.pause()
    } else {
      video.start()
    }
  },

  handlePauseEvt() {
    this.isVideStart = false
  },

  handleStartEvt() {
    this.isVideStart = true
  },

  handleFinisEvt() {
    this.$element('video').setCurrentTime({ currenttime: 0 })
    this.isVideStart = false
  },
}
</script>

<style lang="less">
.header {
  flex-direction: column;
  width: 100%;
  height: 728px;
  padding-top: 104px;
  //background-image: url('/weather/assets/home_header_bg.png');
  //background-size: 100%;
  background: linear-gradient(180deg, #ffffff 0%, #f2e4ff 100%);

  .address {
    padding-left: 32px;

    .ic-plus,
    .ic-address {
      width: 48px;
    }

    .tv-address {
      margin: 0 8px;
      font-size: 36px;
      font-weight: bolder;
      //color: #ffffff;
      color: #000000;
      line-height: 48px;
    }
  }

  .temperature {
    flex-direction: column;
    width: 710px;
    height: 360px;
    background-color: #5c2aed;
    //background: linear-gradient(180deg, #1c29f8 0%, #2584fc 100%);
    border-radius: 40px;
    margin: 106px auto 0;
    padding: 24px 38px;

    .num-box {
      .num {
        height: 200px;
        font-size: 200px;
        font-weight: 600;
        color: #ffffff;
        line-height: 200px;
      }

      .unit {
        width: 54px;
        height: 54px;
        border: 15px solid #ffffff;
        border-radius: 50%;
        margin-left: 18px;
        margin-top: 56px;
      }

      .status {
        font-size: 56px;
        color: #ffffff;
        line-height: 56px;
        align-self: flex-end;
        margin-left: -56px;
      }

      .ic-voice-box {
        visibility: hidden;
        /* margin-left: auto; */
        align-self: flex-start;
        margin-top: 20px;
        .ic-voice {
          width: 64px;
          height: 64px;
        }

        .dot {
          width: 20px;
          height: 20px;
          background-color: red;
          border-radius: 50%;
          margin-left: -15px;
        }
      }
    }

    .status-box {
      justify-content: center;
      align-items: center;
      width: 172px;
      height: 64px;
      margin-top: 20px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 32px;
      .icon {
        width: 56px;
        height: 56px;
      }
      .status-txt {
        font-size: 28px;
        font-weight: bold;
        color: #ffffff;
      }
    }
  }
}
</style>
