<import name="weather-icon" src="./weather-icon.ux"></import>
<import name="thermometer" src="./thermometer.ux"></import>

<template>
  <div class="wrapper">
    <text class="title">{{ title }}</text>
    <div class="swiper-container">
      <swiper class="swiper" style="{{swiperStyle}}">
        <div
          class="swiper-item"
          for="(pIndex, child) in groupList"
          tid="pIndex"
        >
          <div
            class="inner {{currentIndex === pIndex + '-' + cIndex ?  'inner-active' : ''}} {{item.disabled ? 'inner-disabled' : ''}}"
            for="(cIndex, item) in child"
            tid="cIndex"
            @click="handleClick(pIndex, cIndex)"
            style="{{innerStyle}}"
          >
            <text class="time">{{ item.time }}</text>
            <block if="type === '24hours'">
              <text class="temperature" style="{{temperatureStyle}}">
                {{ item.temperature }}°
              </text>
              <weather-icon
                status="{{item.iconCode}}"
                width="56px"
                height="56px"
              ></weather-icon>
            </block>
            <block else>
              <div style="margin-top: 24px">
                <weather-icon
                  status="{{item.iconCode}}"
                  width="44px"
                  height="44px"
                ></weather-icon>
              </div>
              <text class="temperature" style="{{temperatureStyle}}">
                {{ item.maxTemperature }}°
              </text>
              <thermometer
                current-min="{{item.minTemperature}}"
                current-max="{{item.maxTemperature}}"
                container-min="{{thermometerContainer.min}}"
                container-max="{{thermometerContainer.max}}"
              ></thermometer>
              <text class="temperature" style="{{temperatureStyle}}">
                {{ item.minTemperature }}°
              </text>
            </block>
            <text class="wind">{{ item.wind }}</text>
            <text class="level">{{ item.level }}级</text>
            <!-- <text class="score" if="type === '15days'">优</text> -->
          </div>
        </div>
      </swiper>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => {
        return new Array(24).fill({
          time: '12时',
          temperature: 12.2, // 当天温度
          minTemperature: 11, // 最小温度
          maxTemperature: 20, // 最大温度
          weather: 'sunny',
          wind: '东南风',
          level: 2,
          disabled: false,
          iconCode: '1',
        })
      },
    },
    title: {
      type: String,
      default: '24小时天气',
    },
    pageSize: {
      type: Number,
      default: 5,
    },
    temperatureFontSize: {
      type: String,
      default: '36px',
    },
    swiperHeight: {
      type: String,
      default: '360px',
    },
    type: {
      type: String,
      validator: value => {
        return ['24hours', '15days'].indexOf(value) !== -1
      },
      default: '24hours',
    },
  },
  data: {
    currentIndex: '0-1',
  },
  computed: {
    groupList() {
      const groupLen = this.pageSize
      const sourceLen = this.list && this.list.length ? this.list.length : 0
      const len = Math.ceil(sourceLen / groupLen)
      const arr = []

      for (let i = 0; i < len; i++) {
        const start = i * groupLen
        const end = start + groupLen
        const list = this.list.slice(start, end)
        arr.push(list)
      }

      return arr
    },
    temperatureStyle() {
      return {
        fontSize: this.temperatureFontSize,
        lineHeight: this.temperatureFontSize,
      }
    },
    swiperStyle() {
      return {
        height: this.swiperHeight,
      }
    },
    innerStyle() {
      return {
        width: 100 / this.pageSize + '%',
      }
    },
    // 温度计容器
    thermometerContainer() {
      const maxList = this.list.map(it => it.maxTemperature)
      const minList = this.list.map(it => it.minTemperature)

      return {
        min: Math.min(...minList),
        max: Math.max(...maxList),
      }
    },
  },
  handleClick(pIndex, cIndex) {
    this.currentIndex = `${pIndex}-${cIndex}`
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  flex-direction: column;
  padding: 32px 48px 0;
  background-color: #fefefe;
  border-radius: 24px;
  border: 1px solid rgba(192, 202, 216, 0.2);
}

.title {
  font-size: 36px;
  font-weight: 500;
  color: #282829;
  line-height: 36px;
  margin-bottom: 24px;
}

.swiper {
  indicator-color: rgba(216, 216, 216, 1);
  indicator-selected-color: rgba(224, 137, 197, 1);
  indicator-size: 12px;
  indicator-bottom: 24px;

  .swiper-item {
    justify-content: space-between;
    padding-bottom: 56px;
  }

  .inner-active {
    background-color: #33fbc8c4;
  }

  .inner {
    /* flex: 1; */
    /* width: 20%; */
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px 0;
    border-radius: 16px;

    .time {
      font-size: 28px;
      font-weight: 400;
      color: #282829;
    }

    .temperature {
      font-weight: bold;
      color: #282829;
      margin-top: 16px;
      margin-bottom: 24px;
    }

    .wind {
      font-size: 28px;
      color: #282829;
      margin-top: 24px;
      margin-bottom: 16px;
      line-height: 32px;
      lines: 1;
    }

    .level {
      font-size: 28px;
      line-height: 28px;
      color: #666666;
    }

    .score {
      width: 60px;
      height: 32px;
      border-radius: 20px;
      border: 1px solid #7dd6c5;
      color: #7dd6c5;
      text-align: center;
      font-size: 24px;
      line-height: 32px;
      margin-top: 24px;
    }
  }

  .inner-disabled {
    .time,
    .temperature,
    .wind,
    .level {
      color: rgba(40, 40, 41, 0.4);
    }
  }
}
</style>
