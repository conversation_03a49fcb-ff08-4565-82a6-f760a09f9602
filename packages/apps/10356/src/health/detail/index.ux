<template>
  <div class="wrapper">
    <text class="name">{{ name }}</text>
    <text class="desc">{{ desc }}</text>
  </div>
</template>

<script>
export default {
  data: {
    image: '',
    name: '',
    desc: '',
  },
  onInit() {},

  onReady() {},

  onDestroy() {},
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;

  .name {
    font-weight: 600;
    font-size: 40px;
    color: #454545;
    line-height: 56px;
    text-align: left;
    font-style: normal;
    margin: 40px;
  }

  .desc {
    margin: 20px;
  }
}
</style>
