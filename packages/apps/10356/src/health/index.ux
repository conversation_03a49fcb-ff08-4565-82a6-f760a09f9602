<template>
  <div class="wrapper">
    <div class="shopping-list">
      <div
        class="shopping-item-wrapper"
        for="item in items"
        @click="onItemClick(item)"
      >
        <image src="{{item.image}}"></image>
        <text>{{ item.name }}</text>
      </div>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import list from '@/health/list'

export default {
  props: {
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },
  data: {
    items: list,
  },
  onInit() {
    this.getInitData()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  onReady() {},

  onDestroy() {},

  onItemClick(item) {
    router.push({
      uri: 'health/detail',
      params: item,
    })
  },

  handleStorageChange() {
    this.getInitData()
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      this.getInitData()
    }
  },
  async getInitData() {},
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fefefe;
}

.shopping-list {
  flex: 1;
  padding: 40px;
  margin-top: 80px;
  flex-direction: row;
  flex-wrap: wrap;
}

.shopping-item-wrapper {
  width: 312px;
  height: 300px;
  border-radius: 24px;
  border: 2px solid #f2f4f5;
  margin: 10px;
  padding: 32px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  image {
    width: 128px;
    height: 128px;
  }

  text {
    font-weight: 400;
    font-size: 40px;
    color: #112950;
    line-height: 36px;
    margin-top: 38px;
  }
}
</style>
