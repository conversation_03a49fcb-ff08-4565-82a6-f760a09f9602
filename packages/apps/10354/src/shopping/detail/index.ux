<template>
  <div class="wrapper">
    <image src="{{image}}"></image>
    <text class="name">{{ name }}</text>
    <text class="price">{{ price }}</text>
    <div style="flex: 1"></div>
    <div class="button" @click="onButtonClick">
      <text>立即领取优惠券</text>
    </div>
  </div>
</template>

<script>
import clipboard from '@system.clipboard'
import { showToast } from '@quickapp/utils'
export default {
  data: {
    image: '',
    name: '',
    price: '',
    bottomImage: '',
  },
  onInit() {},

  onReady() {},

  onButtonClick() {
    clipboard.set({
      text: 'https://uland.taobao.com/quan/detail?sellerId=4611687822720568379&activityId=6780f3f7651645aa8811530efc10ad4b',
      success: function (data) {
        showToast('已复制链接，请在浏览器打开领券')
      },
      fail: function (data, code) {
        console.log(`handling fail, code = ${code}`)
      },
    })
  },

  onDestroy() {},
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;

  image {
    width: 750px;
    height: 760px;
  }

  .name {
    font-weight: 600;
    font-size: 40px;
    color: #454545;
    line-height: 56px;
    text-align: left;
    font-style: normal;
    margin: 40px;
  }

  .price {
    font-weight: 600;
    font-size: 48px;
    color: #fe0000;
    line-height: 66px;
    text-align: left;
    font-style: normal;
    margin-left: 40px;
  }

  .button {
    width: 670px;
    height: 104px;
    align-self: center;
    justify-self: center;
    margin-bottom: 80px;
    background-color: #f26163;
    border-radius: 52px;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    text {
      font-weight: 600;
      font-size: 48px;
      color: #ffffff;
      line-height: 66px;
      text-align: center;
      font-style: normal;
    }
  }

  .bottom-image {
    width: 750px;
    height: 72px;
    margin-bottom: 0;
  }
}
</style>
