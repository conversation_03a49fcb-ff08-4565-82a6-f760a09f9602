<import name="weather-icon" src="./weather-icon.ux"></import>

<template>
  <div class="two-day">
    <!-- <swiper
      if="{{tipList && tipList.length > 0}}"
      class="swiper"
      indicator="{{false}}"
      vertical="{{true}}"
      autoplay="{{true}}"
      loop="{{true}}"
    >
      <div class="tip-box" for="item in tipList" tid="$idx">
        <image class="icon" src="/weather/assets/ic_warn.png"></image>
        <text class="text">{{ item }}将会下雨</text>
      </div>
    </swiper> -->

    <div class="data-box">
      <div class="today">
        <div class="left">
          <text class="date-txt">今天</text>
          <div class="icon">
            <weather-icon
              status="{{weatherDaily[0] ? weatherDaily[0].code_day  : '0'}}"
              width="56px"
              height="56px"
            ></weather-icon>
          </div>
        </div>
        <div class="right">
          <text class="date">
            {{ weatherDaily[0] ? weatherDaily[0].low : '' }}/{{
              weatherDaily[0] ? weatherDaily[0].high : ''
            }}°
          </text>
          <text class="desc">
            {{
              handleTxt(
                weatherDaily[0] ? weatherDaily[0].text_day : '',
                weatherDaily[0] ? weatherDaily[0].text_night : ''
              )
            }}
          </text>
        </div>
      </div>
      <div class="line"></div>
      <div class="tomorrow">
        <div class="left">
          <text class="date-txt">明天</text>
          <div class="icon">
            <weather-icon
              status="{{weatherDaily[1] ? weatherDaily[1].code_day  : '0'}}"
              width="56px"
              height="56px"
            ></weather-icon>
          </div>
        </div>
        <div class="right">
          <text class="date">
            {{ weatherDaily[1] ? weatherDaily[1].low : '' }}/{{
              weatherDaily[1] ? weatherDaily[1].high : ''
            }}°
          </text>
          <text class="desc">
            {{
              handleTxt(
                weatherDaily[1] ? weatherDaily[1].text_day : '',
                weatherDaily[1] ? weatherDaily[1].text_night : ''
              )
            }}
          </text>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    hourRainList: {
      type: Array,
      default: () => [
        // "2022-07-21T10:00:00+08:00",
        // "2022-08-21T10:00:00+08:00",
        // "2022-09-21T10:00:00+08:00",
      ],
    },
    weatherDaily: {
      type: Array,
      default: () => [
        // {
        //   code_day: "13",
        //   code_night: "9",
        //   date: "2022-07-20",
        //   high: "37",
        //   humidity: "88",
        //   low: "27",
        //   precip: "0.58",
        //   rainfall: "5.40",
        //   text_day: "小雨",
        //   text_night: "阴",
        //   wind_direction: "西南",
        //   wind_direction_degree: "225",
        //   wind_scale: "4",
        //   wind_speed: "23.4",
        // },
        // {
        //   code_day: "13",
        //   code_night: "9",
        //   date: "2022-07-20",
        //   high: "37",
        //   humidity: "88",
        //   low: "27",
        //   precip: "0.58",
        //   rainfall: "5.40",
        //   text_day: "小雨",
        //   text_night: "阴",
        //   wind_direction: "西南",
        //   wind_direction_degree: "225",
        //   wind_scale: "4",
        //   wind_speed: "23.4",
        // }
      ],
    },
  },
  computed: {
    tipList() {
      const addZero = n => (n < 10 ? `0${n}` : n)

      const list = this.hourRainList.map(it => {
        const date = new Date(it)
        const m = date.getMonth() + 1
        const d = date.getDate()
        const hours = date.getHours()
        const minutes = date.getMinutes()

        return `${m}月${d}日${addZero(hours)}:${addZero(minutes)}`
      })

      console.log('下雨列表', list)
      return list
    },
  },

  handleTxt(t1, t2) {
    return t1 === t2 ? t1 : `${t1}转${t2}`
  },
}
</script>

<style lang="less">
.two-day {
  width: 100%;
  flex-direction: column;
  padding: 32px 48px;
  background-color: #fefefe;
  border-radius: 24px;
  border: 1px solid rgba(192, 202, 216, 0.2);

  .swiper {
    height: 88px;
    background-color: rgba(251, 200, 196, 0.2);
    border-radius: 16px;
  }

  .tip-box {
    height: 100%;
    padding: 0 12px;
    align-items: center;

    .icon {
      margin-right: 8px;
      width: 48px;
      height: 48px;
    }

    .text {
      font-size: 28px;
      font-weight: 400;
      color: #282829;
    }
  }

  .data-box {
    justify-content: space-between;
    margin-top: 28px;
    align-items: center;

    .today,
    .tomorrow {
      flex: 1;
      justify-content: space-between;
    }

    .today {
      padding-right: 32px;
    }

    .line {
      width: 2px;
      height: 76px;
      background-color: #eaedfe;
    }

    .tomorrow {
      padding-left: 32px;
    }

    .left,
    .right {
      align-items: center;
      flex-direction: column;
    }

    .date-txt {
      font-size: 32px;
      font-weight: 400;
      color: #333333;
      line-height: 44px;
    }

    .icon {
      width: 56px;
      height: 56px;
      margin-top: 16px;
    }

    .date {
      font-size: 44px;
      font-family: Bebas;
      color: #282829;
      line-height: 44px;
      /* margin-top: 8px; */
      font-weight: bolder;
    }

    .desc {
      font-size: 32px;
      font-weight: 400;
      color: #282829;
      line-height: 44px;
      margin-top: 16px;
    }
  }
}
</style>
