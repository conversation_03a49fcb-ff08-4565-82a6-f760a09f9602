<template>
  <div class="wrapper">
    <div class="input-box">
      <image class="icon" src="/weather/assets/ic_sundown.png"></image>
      <input
        class="input"
        value="{{value}}"
        placeholder="搜索乡镇/城市"
        @change="handleChange"
      />
      <image
        if="{{value}}"
        @click="handleClear"
        class="icon"
        src="/weather/assets/ic_sundown.png"
      ></image>
    </div>
    <text @click="handleSearch">搜索</text>
    <!-- <text if="{{value}}" @click="handleSearch">搜索</text>
    <text else @click="handleCancel">取消</text> -->
  </div>
</template>

<script>
export default {
  data: {
    title: '搜索',
  },

  props: {
    value: {
      type: String,
    },
  },

  onInit() {},

  handleChange(e) {
    this.emitChange(e.value)
  },

  handleClear() {
    this.emitChange('')
  },

  emitChange(data) {
    this.$emit('change', {
      data: data,
    })
  },

  handleSearch() {
    this.$emit('search', {
      data: this.value,
    })
  },

  handleCancel() {
    this.$emit('cancel', {
      data: this.value,
    })
  },
}
</script>

<style lang="less">
.wrapper {
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  height: 88px;
  padding: 0 32px;
}

.input-box {
  align-items: center;
  width: 602px;
  height: 64px;
  padding: 0 26px;
  border-radius: 36px;
  background-color: #f2f2f2;
  margin-right: 20px;

  .icon {
    width: 32px;
    height: 32px;
  }

  .input {
    flex: 1;
    padding-left: 12px;
    font-size: 28px;
  }
}
</style>
