{"id": "10348", "package": "net.yunjiuxing.core", "configCode": "cf_yun<PERSON><PERSON>ore", "name": "云久行", "versionName": "1.0.9", "versionCode": 9, "minPlatformVersion": 1080, "privacyUrl": "https://landing.serveclouds.com/privacy/10348/PrivacyPolicy.html", "userUrl": "https://landing.serveclouds.com/privacy/10348/UserPolicy.html", "icon": "/assets/images/logo.png", "features": [{"name": "system.prompt"}, {"name": "system.router"}, {"name": "system.canvas"}, {"name": "system.shortcut"}, {"name": "system.fetch"}, {"name": "system.geolocation"}, {"name": "system.device"}, {"name": "system.audio"}, {"name": "system.image"}, {"name": "system.network"}, {"name": "system.webview"}, {"name": "system.request"}, {"name": "system.package"}, {"name": "system.storage"}, {"name": "service.account"}, {"name": "system.file"}, {"name": "service.ad"}, {"name": "system.nfc"}, {"name": "system.clipboard"}, {"name": "system.sensor"}], "permissions": [{"origin": "*"}], "config": {"logLevel": "debug", "requestNotificationPermission": false}, "router": {"entry": "pages/Flash", "pages": {"pages/Home": {"component": "index"}, "pages/Flash": {"component": "index"}, "pages/Splash": {"launchMode": "singleTask", "component": "index"}, "attractions/detail": {"component": "index"}, "clean/cleaning": {"component": "index"}, "weather/City": {"component": "index"}}}, "display": {"titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141", "themeMode": 0, "menuBarData": {"menuBar": false}, "pages": {"pages/Home": {"titleBarText": "首页", "titleBar": false, "themeMode": 0, "menuBarData": {"menuBar": false}, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0}, "pages/Flash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Splash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "fitCutout": "portrait", "fullScreen": true, "menu": true}}}}