<import
  name="weather-page"
  src="../../components/WeatherPage/index.ux"
></import>
<import
  name="mine-page"
  src="@quickapp/mc-ui/components/setting-page.ux"
></import>
<import name="clean-page" src="../../clean/index.ux"></import>
<import name="novel-page" src="../../novel/NovelPage/bookstore.ux"></import>
<import name="pet-page" src="@/pet/index.ux"></import>

<!-- <import name="info-page" src="../../components/InfoPage.ux"></import> -->
<import name="service" src="@quickapp/mc-ui/components/service.ux"></import>

<template>
  <tabs class="wrapper" @change="handleChange" index="{{currentIndex}}">
    <tab-content class="content">
      <div class="content-box" for="item in pageList" tid="$idx">
        <component
          id="{{item.pageComponent}}"
          is="{{item.pageComponent}}"
          index="{{$idx}}"
          current-index="{{currentIndex}}"
          life-cycle-show="{{lifeCycleShow}}"
        ></component>
      </div>
    </tab-content>
    <tab-bar class="tab-bar">
      <div for="item in pageList" tid="$idx" class="tab-bar-item">
        <block if="{{item.pageComponent === 'book-keep-page'}}">
          <div class="center-tab">
            <image class="icon" src="/assets/images/ic_add.png"></image>
          </div>
          <text class="tab-text">{{ item.text }}</text>
        </block>
        <block else>
          <text class="tab-text">{{ item.text }}</text>
        </block>
      </div>
    </tab-bar>
    <service></service>
  </tabs>
</template>

<script>
import device from '@quickapp/business/lib/device'
import { trackEvent } from '@quickapp/business'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'
import config from '@quickapp/business/lib/config'

export default setPageMenuConfig({
  private: {
    lifeCycleShow: 0,
    pageList: [
      {
        pageComponent: 'weather-page',
        text: '天气',
      },
      {
        pageComponent: 'pet-page',
        text: '宠物',
      },
      {
        pageComponent: 'mine-page',
        text: '我的',
      },
    ],
    currentIndex: 0,
  },

  async onInit() {
    await config.updateConfig()
    let showClean = config.getAdConfig()['show_clean']
    let showNovel = config.getAdConfig()['show_novel']
    if (showClean) {
      this.pageList = [
        ...this.pageList,
        {
          pageComponent: 'clean-page',
          text: '清理',
        },
      ]
    }
    if (showNovel) {
      this.pageList = [
        {
          iconPath: '/novel/assets/ic_bookstore.webp',
          selectedIconPath: '/novel/assets/ic_bookstore_active.webp',
          pageComponent: 'novel-page',
          text: '小说',
        },
        ...this.pageList,
      ]
      this.currentIndex = -1
      setTimeout(() => {
        this.currentIndex = 0
      })
    }
  },

  onShow() {
    this.lifeCycleShow++
    trackEvent({
      category: 'page',
      action: 'show',
      opt_label: 'main',
    })

    const weatherPage = this.$child('weather-page')
    if (weatherPage) {
      weatherPage.getInitData()
    }
    const luckPage = this.$child('luck-page')
    if (luckPage) {
      luckPage.getInitData()
    }
  },

  onBackPress() {
    return false
  },
  handleAdClose() {},

  handleChange(evt) {
    this.currentIndex = evt.index
  },
})
</script>

<style lang="less">
@import '../../assets/styles/style.less';

.wrapper {
  width: 100%;
  height: 100%;
  background-color: #50596c;
}

.tab-bar {
  display: flex;
  width: 100%;
  height: 135px;
  background-color: #50596c;
  border-top: 1px solid#323845;
}

.center-tab {
  justify-content: center;
  align-items: center;
  background-color: #50596c;
  width: 60px;
  height: 60px;
  border-radius: 50%;

  .icon {
    width: 35px;
    height: 35px;
  }
}

.tab-bar-item {
  width: 20%;
  flex-direction: column;
  align-items: center;
  height: 100px;
  background-color: #50596c;

  .tab-bar-icon {
    width: 52px;
    height: 52px;
  }

  .tab-bar-icon-active {
    width: 52px;
    height: 52px;
  }

  .tab-text {
    /* text-align: center; */
    font-size: 38px;
    color: #ffffff;
    line-height: 100px;
  }

  .tab-text:active {
    color: #ffde45;
  }
}
</style>
