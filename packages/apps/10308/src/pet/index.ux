<template>
  <div class="container">
    <text class="main-title">小小的宠物 大大的爱</text>
    <list class="list">
      <list-item for="{{petList}}" class="list-item">
        <div class="card" @click="goToDetail($item)">
          <div class="image-container">
            <image src="{{$item.image[0]}}" class="pet-image"></image>
          </div>
          <div class="info">
            <text class="title">{{ $item.name }}</text>
            <div class="tags">
              <text for="{{tag in $item.tag}}" class="tag">{{ tag }}</text>
            </div>
            <text class="region">{{ $item.region }}</text>
          </div>
        </div>
      </list-item>
    </list>
  </div>
</template>

<script>
import router from '@system.router'
import list from './list.js'

export default {
  data: {
    petList: [],
  },
  onInit() {
    this.petList = list
  },
  goToDetail(item) {
    router.push({
      uri: 'pet/detail',
      params: {
        id: item.id,
      },
    })
  },
}
</script>

<style>
.container {
  flex-direction: column;
  background: linear-gradient(225deg, #fffcf6 0%, #fbf4ff 100%);
  padding: 20px;
  width: 100%;
  height: 100%;
}

.main-title {
  color: #155157;
  font-size: 48px;
  font-weight: bold;
  text-align: center;
  margin-top: 40px;
  margin-bottom: 40px;
}

.list {
  flex: 1;
}

.list-item {
  margin-bottom: 20px;
}

.card {
  width: 100%;
  background-color: #ffffff;
  border-radius: 30px;
  padding: 20px;
  align-items: center;
}

.image-container {
  width: 200px;
  height: 200px;
  border-radius: 20px;
  margin-right: 20px;
  justify-content: center;
  align-items: center;
  background-color: #ffebee;
}

.pet-image {
  width: 150px;
  height: 150px;
}

.info {
  flex: 1;
  flex-direction: column;
}

.title {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 10px;
}

.tags {
  margin-bottom: 10px;
}

.tag {
  background-color: #e3f2fd;
  color: #2196f3;
  padding: 5px 15px;
  border-radius: 20px;
  margin-right: 10px;
  font-size: 24px;
}

.region {
  font-size: 24px;
  color: #9e9e9e;
}
</style>
