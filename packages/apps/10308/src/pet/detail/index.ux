<template>
  <div class="container">
    <image src="{{image[1]}}" class="pet-image"></image>
    <text class="title">{{ name }}</text>
    <image src="{{image[2]}}" class="pet-image2"></image>
  </div>
</template>

<script>
import list from '@/pet/list'

export default {
  data: {
    id: '',
    name: '',
    image: [],
  },
  onInit() {
    let pet = list.find(item => item.id == this.id)
    this.name = pet.name
    this.image = pet.image
  },
}
</script>

<style>
.container {
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.title {
  font-size: 48px;
  font-weight: bold;
  margin-top: 40px;
  margin-bottom: 40px;
  text-align: center;
}

.pet-image {
  width: 750px;
  height: 800px;
}

.pet-image2 {
  width: 100%;
  min-height: 750px;
  height: auto;
}
</style>
