import { storage } from '@quickapp/utils'
import { generateOrder } from '@quickapp/utils/lib/api/ocr/user'
import { dncBase64 } from '@quickapp/business/lib/crypto'
import quickAppAlipay from '@service.alipay'

export function setLocalGuide() {
  return storage.set('guide', true)
}

export function getLocalGuide() {
  return storage.get('guide')
}

/**
 * 设置付费协议
 */
export function setLocalRechargePro(value) {
  return storage.set('auto-renew', value)
}

/**
 * 获取付费协议
 */
export function getLocalRechargePro() {
  return storage.get('auto-renew')
}

/**
 * 支付宝支付
 * id: vip ID
 * payType: 支付类型
 */
export function alipay(id) {
  return new Promise((resolve, reject) => {
    generateOrder(id, 1)
      .then(res => {
        if (res && res.aliOrderInfo) {
          console.log('支付信息', res.aliOrderInfo)
          quickAppAlipay.pay({
            orderInfo: res.aliOrderInfo,
            callback(ret) {
              console.log('alipay callback', JSON.stringify(ret))
              resolve(ret)
            },
          })
        } else {
          reject({
            message: '网络错误',
          })
        }
      })
      .catch(e => {
        reject(e)
      })
  })
}

/**
 * base64 转 ArrayBuffer
 * @param bs64Data
 * @returns {*}
 */
export function base64toUnit8Array(bs64Data) {
  let base64Str = dncBase64(bs64Data)
  return convert_word_array_to_uint8Array(base64Str)
}

function convert_word_array_to_uint8Array(wordArray) {
  let len = wordArray.words.length,
    u8_array = new Uint8Array(len << 2),
    offset = 0,
    word,
    i

  for (i = 0; i < len; i++) {
    word = wordArray.words[i]
    u8_array[offset++] = word >> 24
    u8_array[offset++] = (word >> 16) & 0xff
    u8_array[offset++] = (word >> 8) & 0xff
    u8_array[offset++] = word & 0xff
  }
  return u8_array
}
