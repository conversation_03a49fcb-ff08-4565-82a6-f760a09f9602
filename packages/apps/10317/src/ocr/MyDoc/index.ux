<import name="doc-com" src="../Doc.ux"></import>

<template>
  <div class="wrapper">
    <div class="header" @click="back">
      <image class="header__icon" src="/ocr/assets/ic_left.webp"></image>
      <text>返回</text>
    </div>
    <doc-com></doc-com>
  </div>
</template>

<script>
import router from '@system.router'

export default {
  back() {
    router.back()
  },
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
}
.header {
  align-items: center;
  position: fixed;
  left: 20px;
  top: 40px;
  height: 88px;

  &__icon {
    width: 30px;
    margin-right: 10px;
  }

  text {
    color: #000;
    font-weight: bold;
  }
}
</style>
