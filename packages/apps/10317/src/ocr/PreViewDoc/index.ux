<import
  name="permission-dialog"
  src="@quickapp/mc-ui/components/permission-dialog.ux"
></import>
<template>
  <div class="wrapper">
    <list class="list">
      <list-item class="item" type="item">
        <text class="title">{{ content }}</text>
      </list-item>
    </list>
    <div class="footer">
      <input
        class="footer__btn"
        type="button"
        value="复制"
        @click="handleCopy"
      />
      <input class="footer__btn" type="button" value="打开" @click="openFile" />
    </div>
    <permission-dialog id="permissionDialog"></permission-dialog>
  </div>
</template>

<script>
import file from '@system.file'
import clipboard from '@system.clipboard'
import { showToast } from '@quickapp/utils'
import router from '@system.router'

export default {
  private: {
    content: '',
  },

  protected: {
    fileUri: '',
  },

  onReady() {
    if (this.fileUri) {
      this.$child('permissionDialog')?.show()
      file.readText({
        uri: this.fileUri,
        success: data => {
          console.log('readText text: ' + data.text)
          this.content = data.text
        },
        fail: (data, code) => {
          console.log(`handling fail, code = ${code}`)
        },
        complete: () => {
          this.$child('permissionDialog')?.hide()
        },
      })
    }
  },

  handleCopy() {
    this.$child('permissionDialog')?.show()
    clipboard.set({
      text: this.content,
      success: function (data) {
        showToast('复制成功')
      },
      fail: function (data, code) {
        showToast('复制失败，您可能未给权限')
      },
      complete: () => {
        this.$child('permissionDialog')?.hide()
      },
    })
  },

  openFile() {
    router.push({
      uri: this.fileUri,
    })
  },
}
</script>

<style lang="less">
.wrapper {
  height: 100%;
  padding: 40px;
  flex-direction: column;
}

.list {
  flex: 1;
}

.footer {
  height: 100px;
  align-items: center;
  justify-content: space-between;

  &__btn {
    flex: 1;
    height: 88px;
    background-color: #2d40e9;
    color: white;
    border-radius: 20px;
    margin: 0 15px;
  }
}
</style>
