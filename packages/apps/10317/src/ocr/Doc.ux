<import name="file-list" src="./FileList.ux"></import>

<template>
  <div class="page">
    <text class="title">所有文档({{ allLen }})</text>
    <div class="certificates" @click="toCertificatesListPage">
      <div class="certificates-text">
        <text class="certificates__text">证件扫描</text>
        <text class="certificates__num">{{ certificatesLen }}个</text>
      </div>
      <image
        class="certificates__icon"
        src="/ocr/assets/ill/ill_certificates.webp"
      ></image>
    </div>
    <file-list id="fileList" @init="handleFileListInit"></file-list>
  </div>
</template>

<script>
import router from '@system.router'
import file from '@system.file'
import { filePath } from './config/files'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    // 修改 lifeCycleShow 的值，执行 handleLifeCycleShow
    lifeCycleShow: {
      type: Number,
    },
  },

  data() {
    return {
      allLen: 0,
      certificatesLen: 0,
    }
  },

  onInit() {
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('lifeCycleShow', 'handleLifeCycleShow')
  },

  onReady() {
    // this.getDocList()
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getDocList()
    }
  },

  handleLifeCycleShow() {
    this.getDocList()
  },

  getDocList() {
    this.$child('fileList').getDocList()
  },

  toCertificatesListPage() {
    router.push({
      uri: 'ocr/CertificatesList',
    })
  },

  handleFileListInit({ detail }) {
    console.log('handleFileListInit', JSON.stringify(detail))
    this.allLen = detail.length

    file.list({
      uri: filePath,
      success: data => {
        // this.allLen
        this.certificatesLen = data.fileList.length - this.allLen
      },
    })
  },
}
</script>

<style lang="less">
.page {
  flex-direction: column;
  width: 100%;
  padding: 0 28px;
  overflow: auto;
  background-color: #18222f;
}

.title {
  font-size: 32px;
  font-weight: bold;
  color: white;
  line-height: 32px;
  margin-top: 116px;
}

.certificates {
  width: 694px;
  height: 240px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background-image: url('/ocr/assets/ill/ill_bg_certificates.webp');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 12px;
  padding: 32px;
  margin-top: 48px;

  .certificates-text {
    height: 100%;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
  }

  &__icon {
    width: 72px;
    height: 72px;
    margin-left: 24px;
  }

  &__text {
    font-size: 44px;
    color: white;
  }

  &__num {
    font-size: 32px;
    color: white;
  }
}

.ad-box {
  width: 100%;
  flex-direction: column;
}
</style>
