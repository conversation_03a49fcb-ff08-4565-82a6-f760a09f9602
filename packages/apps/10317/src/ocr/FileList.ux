<template>
  <div class="list">
    <div if="{{!list || !list.length}}" class="no-data-box">
      <image class="no-data" src="/ocr/assets/no_data.webp"></image>
    </div>
    <div
      else
      class="list__item"
      for="item in list"
      tid="$idx"
      @click="openFile(item)"
    >
      <image class="list__item__icon" src="{{item.icon}}"></image>
      <div class="list__item__info">
        <text class="list__item__info__title">
          {{ item.fienName }}
        </text>
        <text class="list__item__info__time">
          {{ item.formatTime }}
        </text>
        <div class="list__item__info__tags">
          <text class="list__item__info__tags__item">
            {{ item.typeText }}
          </text>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import file from '@system.file'
import { filePath, fileTypeList } from './config/files'
import dayjs from 'dayjs'
import { getExtension } from '@quickapp/utils'
import router from '@system.router'

export default {
  props: {
    filterType: {
      default: 'not_certificates',
    },
    readPath: {
      // internal://mass/download
      default: filePath,
    },
  },

  data() {
    return {
      list: [],
    }
  },

  onInit() {
    this.getDocList()
  },

  getDocList() {
    file.list({
      uri: this.readPath,
      success: data => {
        this.list = data.fileList
          .map(it => {
            const arr = it.uri.split('/')
            const [id, type, timeAddExt] = arr[arr.length - 1].split('_')
            const [time, extension] = timeAddExt.split('.')
            const result = fileTypeList.find(item => item.id === Number(id))

            return {
              ...it,
              fienName: result.title + '.' + extension,
              typeText: result.title,
              type: type,
              icon: result.icon,
              fileTypeResult: result,
              formatTime: dayjs(new Date(it.lastModifiedTime)).format(
                'YYYY-MM-DD HH:mm'
              ),
            }
          })
          .filter(it => {
            if (this.filterType === 'not_certificates') {
              return it.type !== 'certificates'
            } else if (this.filterType === 'certificates') {
              return it.type === 'certificates'
            }
            return true
          })
          .sort(
            (a, b) => Number(b.lastModifiedTime) - Number(a.lastModifiedTime)
          )

        this.$emit('init', this.list)
        console.log('getDocList list', JSON.stringify(data))
      },
    })
  },

  openFile(item) {
    const extension = getExtension(item.uri)
    if (extension === '.pdf' || extension === '.xls') {
      router.push({
        uri: item.uri,
      })
      return
    }

    if (['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(extension)) {
      router.push({
        uri: 'ocr/ImgPreview',
        params: {
          uri: item.uri,
          certificatesType: item.fileTypeResult,
        },
      })
      return
    }

    router.push({
      uri: 'ocr/PreViewDoc',
      params: {
        fileUri: item.uri,
      },
    })
  },
}
</script>

<style lang="less">
.no-data-box {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  .no-data {
    width: 430px;
  }
}

.list {
  flex-direction: column;

  &__item {
    width: 694px;
    height: 220px;
    background-color: #ffffff;
    border-radius: 24px;
    align-items: center;
    margin-top: 28px;
    padding: 0 30px;

    &__icon {
      width: 160px;
      height: 160px;
      margin-right: 20px;
    }

    &__info {
      flex-direction: column;

      &__title {
        font-size: 32px;
        font-weight: bold;
        color: #333333;
      }

      &__time {
        font-size: 24px;
        font-weight: 400;
        color: rgba(51, 51, 51, 0.6);
        line-height: 34px;
        margin-top: 12px;
      }

      &__tags {
        margin-top: 12px;
        &__item {
          text-align: center;
          padding: 0 10px;
          height: 40px;
          background-color: #fff5d5;
          border-radius: 10px;
          font-size: 20px;
          font-weight: bolder;
          color: #9e9a6c;
          line-height: 28px;
          margin-right: 14px;
        }
      }
    }
  }
}
</style>
