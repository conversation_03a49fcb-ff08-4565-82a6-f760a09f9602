<template>
  <div class="wrapper">
    <image class="back" src="../assets/back.webp" @click="onBackClick"></image>
    <div class="cleaning" if="cleaning">
      <image src="../assets/cleaning-circle.webp"></image>
      <text class="text2">正在扫描...</text>
      <div class="content-bg"></div>
      <div class="content">
        <div class="item" for="item in items">
          <text>{{ item.text }}</text>
          <image src="{{item.img}}" class="{{item.style}}"></image>
        </div>
      </div>
    </div>
    <div class="finish" else>
      <image src="../assets/check-big.webp"></image>
      <text class="text1">优化完成</text>
      <text class="text2">{{ num }}GB内存释放</text>
    </div>
  </div>
</template>

<script>
import router from '@system.router'

export default {
  data: {
    cleaning: true,
    num: 0,
    items: [
      {
        text: '应用垃圾',
        img: '../assets/loading.webp',
        style: '',
      },
      {
        text: '垃圾文件',
        img: '../assets/loading.webp',
        style: '',
      },
      {
        text: '安装包',
        img: '../assets/loading.webp',
        style: '',
      },
      {
        text: '卸载残留',
        img: '../assets/loading.webp',
        style: '',
      },
    ],
  },

  onBackClick() {
    router.back()
  },

  onReady() {
    // this.num = (Math.random() * 5).toFixed(2)
    for (let i = 0; i < this.items.length; i++) {
      setTimeout(() => {
        this.items[i].style = 'rotate_anim'
        this.items = [...this.items]
        setTimeout(() => {
          this.items[i].style = ''
          this.items[i].img = '../assets/check.webp'
          this.items = [...this.items]
        }, 2000)
      }, i * 2000)
    }
    setTimeout(() => {
      this.cleaning = false
    }, 8000)
  },

  onDestroy() {},
  onButtonClick() {
    router.back()
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #444ce4;

  .back {
    width: 88px;
    height: 88px;
    padding: 16px;
    margin: 60px 16px;
  }

  .cleaning {
    width: 100%;
    height: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    image {
      width: 560px;
      height: 560px;
    }

    .text2 {
      font-size: 30px;
      color: white;
      font-weight: bold;
      margin-top: 20px;
    }

    .content {
      flex-direction: column;
      width: 100%;
      margin-top: 100px;
      padding: 0 20px;

      .item {
        width: 100%;
        height: 88px;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;

        text {
          font-size: 36px;
          color: #333;
          font-weight: bold;
        }

        image {
          width: 56px;
          height: 56px;
        }
      }
    }

    .content-bg {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 630px;
      background-color: white;
      border-top-left-radius: 60px;
      border-top-right-radius: 60px;
    }

    .clean-button {
      width: 560px;
      height: 88px;
      border: 4px solid #333;
      border-radius: 40px;
      justify-content: center;
      align-items: center;
      margin-top: 100px;

      text {
        font-size: 36px;
        color: #333;
        font-weight: bold;
      }

      :active {
        border: 4px solid #333;
      }
    }
  }

  .finish {
    flex-direction: column;
    justify-content: center;
    align-items: center;

    image {
      width: 160px;
      height: 160px;
    }

    .text1 {
      font-size: 54px;
      color: white;
      font-weight: bold;
      margin-top: 20px;
    }

    .text2 {
      font-size: 30px;
      color: white;
      font-weight: normal;
      margin-top: 20px;
    }
  }
}
.rotate_anim {
  animation-name: rotate;
  animation-duration: 0.8s;
  animation-fill-mode: none;
  animation-iteration-count: -1;
  animation-timing-function: linear;
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
