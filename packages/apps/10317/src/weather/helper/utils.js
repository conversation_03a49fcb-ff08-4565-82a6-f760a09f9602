/**
 * 您可以将常用的方法、或系统 API，统一封装，暴露全局，以便各页面、组件调用，而无需 require / import.
 */
const prompt = require('@system.prompt')
import geolocation from '@system.geolocation'
import storage from '@system.storage'
import { storage as storagePromise } from '@quickapp/utils'

/**
 * 拼接 url 和参数
 */
function queryString(url, query) {
  let str = []
  for (let key in query) {
    str.push(key + '=' + query[key])
  }
  let paramStr = str.join('&')
  return paramStr ? `${url}?${paramStr}` : url
}

function showToast(message = '', duration = 0) {
  if (!message) return
  prompt.showToast({
    message: message,
    duration,
  })
}

function getLocation() {
  return new Promise((resolve, reject) => {
    geolocation.getLocation({
      success: data => {
        resolve(data)
      },
      fail: function (data, code) {
        if (code === 201) {
          setUserRejectPosition()
        }
        console.error('getLocation', data, code)
        reject({ data, code })
      },
    })
  })
}

function reverseGeocodeQuery(latitude, longitude) {
  return new Promise((resolve, reject) => {
    geolocation.reverseGeocodeQuery({
      latitude: latitude,
      longitude: longitude,
      coordType: 'gcj02',
      includePoiInfo: true,
      success: ret => {
        resolve(ret)
      },
      fail: (data, code) => {
        reject({ data, code })
      },
    })
  })
}

/**
 * 用户存本地的位置
 */
function getLocationCityInfo() {
  return new Promise(resolve => {
    storage.get({
      key: 'location',
      success: function (data) {
        let location

        try {
          location = JSON.parse(data)
        } catch (error) {
          location = null
        }

        resolve(location)
      },
      fail: function () {
        resolve(null)
      },
    })
  })
}

/**
 * 设置用户选择的城市
 */
function setLocationCityInfo(data) {
  return new Promise((resolve, reject) => {
    storage.set({
      key: 'location',
      value: JSON.stringify(data),
      success: function (data) {
        console.log('设置成功', data)
        resolve(data)
      },
      fail: function (data, code) {
        reject({
          data,
          code,
        })
      },
    })
  })
}

/**
 * 获取定位信息
 */
function getPostionInfo() {
  return new Promise(async (resolve, reject) => {
    try {
      // 然后是定位
      const { latitude, longitude } = await getLocation()
      const cityInfo = await reverseGeocodeQuery(latitude, longitude)

      resolve({
        ...cityInfo,
        latitude: latitude,
        longitude: longitude,
      })
    } catch (error) {
      reject(error)
    }
  })
}

function getCityInfo() {
  return new Promise(async (resolve, reject) => {
    try {
      // 用户选的优先级高
      const location = await getLocationCityInfo()

      if (location) {
        resolve(location)
      } else {
        resolve({
          code: 'c1101',
          street: '北京市',
          district: '北京市',
          city: '北京市',
          province: '北京市',
          level: 2,
        })
      }
    } catch (error) {
      console.error('getCityInfo', error)
      reject(error)
    }
  })
}

/**
 * 用户拒绝权限，设置本地信息
 * @returns {Promise | Promise<unknown>}
 */
function setUserRejectPosition() {
  return storagePromise.set('user-reject-position', true)
}

/**
 * 获取用户拒绝是否拒绝
 * @returns {Promise | Promise<unknown>}
 */
function getUserRejectPosition() {
  return storagePromise.get('user-reject-position')
}

function removeUserRejectPosition() {
  return storagePromise.delete('user-reject-position')
}

const MyStorage = {
  get(key) {
    return new Promise((resolve, reject) => {
      storage.get({
        key: key,
        success: function (data) {
          console.log('本地....', key, data)
          if (data) {
            try {
              resolve(JSON.parse(data))
            } catch (e) {
              console.error('storage.get 报错了...', e)
              reject(e)
            }
          } else {
            resolve(null)
          }
        },
        fail: function (data, code) {
          console.log('错误....')
          reject({ data, code })
        },
      })
    })
  },
  set(key, value) {
    return new Promise((resolve, reject) => {
      storage.set({
        key: key,
        value: JSON.stringify(value),
        success: function (data) {
          resolve(data)
        },
        fail: function (data, code) {
          console.error('storage.set 报错了...', e)
          reject({ data, code })
        },
      })
    })
  },
}
export default {
  showToast,
  queryString,
  getCityInfo,
  getLocationCityInfo,
  setLocationCityInfo,
  getPostionInfo,
  getUserRejectPosition,
  setUserRejectPosition,
  removeUserRejectPosition,
  MyStorage,
}
