{"id": "10227", "package": "shou.hen.oc.red", "configCode": "cf_ghtevc", "name": "守恒扫描OCR", "versionName": "1.2.71", "versionCode": 191, "privacyUrl": "https://xy.serveclouds.com/landing/10227/PrivacyPolicy.html", "userUrl": "https://xy.serveclouds.com/landing/10227/UserPolicy.html", "guidelinesForTortClaimsUrl": "https://app-h5.springtool.cn/sjqnsmw/agreement/guidelines-for-tort-claims.html", "questionUrl": "https://wj.qq.com/s2/********/44b7", "paymentAgreement": "https://app-h5.springtool.cn/sjqnsmw/agreement/payment_agreement.html", "minPlatformVersion": 1100, "icon": "/assets/images/logo.webp", "features": [{"name": "system.prompt"}, {"name": "system.router"}, {"name": "system.shortcut"}, {"name": "system.storage"}, {"name": "system.device"}, {"name": "system.ad"}, {"name": "system.network"}, {"name": "system.package"}, {"name": "system.webview"}, {"name": "system.request"}, {"name": "service.account"}, {"name": "system.sensor"}, {"name": "system.file"}, {"name": "system.media"}, {"name": "system.image"}, {"name": "system.clipboard"}, {"name": "system.nfc"}, {"name": "system.clipboard"}], "permissions": [{"origin": "*"}], "template/official": "demo-template", "config": {"logLevel": "debug", "requestNotificationPermission": false}, "router": {"entry": "pages/Flash", "pages": {"pages/Flash": {"component": "index"}, "pages/Splash": {"launchMode": "singleTask", "component": "index"}, "pages/Home": {"component": "index"}, "pages/Web": {"component": "index"}, "pages/Service": {"component": "index"}, "pages/ServiceNoAd": {"component": "index"}, "pages/MyDoc": {"component": "index"}, "pages/ImgPreview": {"component": "index"}, "pages/PreViewDoc": {"component": "index"}, "pages/Guide": {"component": "index"}, "pages/Camera": {"component": "index"}, "pages/About": {"component": "index"}, "pages/Recharge": {"component": "index"}, "pages/CertificatesList": {"component": "index"}, "weather/City": {"component": "index"}, "clean/cleaning": {"component": "index"}, "novel/bookstore-detail": {"component": "index"}, "novel/introduce": {"component": "index"}, "novel/detail": {"component": "index"}, "novel/reader": {"component": "index"}}}, "display": {"titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141", "themeMode": 0, "menuBarData": {"menuBar": false}, "menu": true, "pages": {"pages/Home": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "menuBarData": {"menuBar": false}, "fullScreen": false}, "pages/MyDoc": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "menuBarData": {"menuBar": false}, "fullScreen": false}, "pages/Web": {"titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141"}, "pages/CertificatesList": {"titleBarText": "证件扫描", "titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141"}, "pages/Flash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Splash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "fitCutout": "portrait", "fullScreen": true, "menu": true}, "pages/Guide": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Camera": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Recharge": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/ImgPreview": {"component": "index", "titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Service": {"titleBarText": "在线客服"}, "pages/MyDox": {"titleBarText": "我的文档", "titleBarBackgroundColor": "#EDF6FF", "titleBarTextColor": "#000000"}, "pages/About": {"titleBarText": "关于", "titleBarBackgroundColor": "#EDF6FF", "titleBarTextColor": "#000000"}, "UnionAd/AdReward": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}, "UnionAd/AdLanding": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}}}}