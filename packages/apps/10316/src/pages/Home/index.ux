<import name="answer-page" src="../../components/AnswerPage/index.ux"></import>
<import name="clean-page" src="../../clean/index.ux"></import>
<import name="novel-page" src="../../novel/NovelPage/bookstore.ux"></import>
<import name="luck-page" src="@/luck/index.ux"></import>
<import
  name="weather-page"
  src="../../components/WeatherPage/index.ux"
></import>
<import
  name="mine-page"
  src="@quickapp/mc-ui/components/setting-page.ux"
></import>
<import name="service" src="@quickapp/mc-ui/components/service.ux"></import>

<template>
  <tabs class="wrapper" @change="handleChange" index="{{currentIndex}}">
    <tab-content class="content">
      <div class="content-box" for="item in pageList" tid="$idx">
        <component
          id="{{item.pageComponent}}"
          is="{{item.pageComponent}}"
          index="{{$idx}}"
          current-index="{{currentIndex}}"
          title="{{title}}"
          list="{{optionsList}}"
        ></component>
      </div>
    </tab-content>
    <tab-bar class="tab-bar">
      <div for="item in pageList" tid="$idx" class="tab-bar-item">
        <block if="{{item.pageComponent === 'book-keep-page'}}">
          <div class="center-tab">
            <image class="icon" src="/assets/images/ic_add.png"></image>
          </div>
          <text class="tab-text">{{ item.text }}</text>
        </block>
        <block else>
          <!-- <image
            if="{{currentIndex === $idx}}"
            class="tab-bar-icon-active"
            src="{{item.selectedIconPath}}"
          /> -->
          <!-- <image else class="tab-bar-icon" src="{{item.iconPath}}" /> -->
          <text class="tab-text">{{ item.text }}</text>
        </block>
      </div>
    </tab-bar>
    <service></service>
  </tabs>
</template>

<script>
import device from '@quickapp/business/lib/device'
import { trackEvent } from '@quickapp/business'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'
import { storage } from '@quickapp/utils'
import config from '@quickapp/business/lib/config'

export default setPageMenuConfig({
  private: {
    pageList: [
      {
        pageComponent: 'weather-page',
        text: '天气',
      },
      {
        pageComponent: 'luck-page',
        text: '随心转',
      },
      {
        pageComponent: 'answer-page',
        text: '答题',
      },
      {
        pageComponent: 'mine-page',
        text: '我的',
      },
    ],
    currentIndex: 0,
    title: '',
    optionsList: [],
  },

  async onInit() {
    await config.updateConfig()
    let showClean = config.getAdConfig()['show_clean']
    console.log('show clean: ', showClean)
    if (showClean) {
      this.pageList = [
        {
          pageComponent: 'clean-page',
          text: '清理',
        },
        ...this.pageList,
      ]
    }
    let showNovel = config.getAdConfig()['show_novel']
    if (showNovel) {
      this.pageList = [
        {
          iconPath: '/novel/assets/ic_bookstore.webp',
          selectedIconPath: '/novel/assets/ic_bookstore_active.webp',
          pageComponent: 'novel-page',
          text: '小说',
        },
        ...this.pageList,
      ]
      this.currentIndex = -1
      setTimeout(() => {
        this.currentIndex = 0
      })
    }
  },

  onShow() {
    trackEvent({
      category: 'page',
      action: 'show',
      opt_label: 'main',
    })

    const luckPage = this.$child('luck-page')
    const weatherPage = this.$child('weather-page')
    if (luckPage) {
      luckPage.getInitData()
    }
    if (weatherPage) {
      weatherPage.getInitData()
    }
  },

  onBackPress() {
    return false
  },
  handleAdClose() {},

  handleChange(evt) {
    this.currentIndex = evt.index
  },
})
</script>

<style lang="less">
@import '../../assets/styles/style.less';

.wrapper {
  width: 100%;
  height: 100%;
  background-color: #f8f8f8;
}

.tab-bar {
  display: flex;
  width: 100%;
  height: 135px;
  background-color: white;
}

.center-tab {
  justify-content: center;
  align-items: center;
  background-color: #fbdf4c;
  width: 60px;
  height: 60px;
  border-radius: 50%;

  .icon {
    width: 35px;
    height: 35px;
  }
}

.tab-bar-item {
  width: 20%;
  flex-direction: column;
  align-items: center;
  height: 100px;
  background-color: white;

  .tab-bar-icon {
    width: 52px;
    height: 52px;
  }

  .tab-bar-icon-active {
    width: 52px;
    height: 52px;
  }

  .tab-text {
    text-align: center;
    color: #424242;
    line-height: 100px;
    font-size: 32px;
  }

  .tab-text:active {
    color: #c59eff;
  }
}
</style>
