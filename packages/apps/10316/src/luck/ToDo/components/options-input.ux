<template>
  <div class="options">
    <input type="text" value="{{localOption}}" @change="handleInputChange" />
    <div class="cha" @click="handleRemove">
      <image class="cha" src="/luck/assets/cha.webp"></image>
    </div>
  </div>
</template>

<script>
export default {
  props: ['option', 'index'],

  data() {
    return {
      localOption: this.option || '',
    }
  },

  handleInputChange(e) {
    this.localOption = e.value
    this.$emit('updateOption', {
      index: this.index,
      value: e.value,
    })
  },

  handleRemove() {
    this.$emit('remove', {
      index: this.index,
    })
  },
}
</script>

<style lang="less">
.options {
  padding: 10px;
  padding-right: 20px;
  display: flex;
  border: 1px solid #979797;
  width: 100%;
  border-radius: 20px;
  align-items: center;
  margin-bottom: 20px;
  background-color: #ffffff;

  input {
    flex: 1;
  }
  .cha {
    width: 30px;
    height: 30px;
    cursor: pointer;
  }
}
</style>
