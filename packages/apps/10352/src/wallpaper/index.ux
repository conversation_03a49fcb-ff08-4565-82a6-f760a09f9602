<template>
  <div class="page">
    <text class="title">壁纸选择</text>
    <list class="grid">
      <list-item
        for="{{imageList}}"
        class="grid-item"
        onclick="goToDetail($item.src)"
      >
        <image src="{{$item.src}}" class="wallpaper-image"></image>
      </list-item>
    </list>
  </div>
</template>

<script>
import router from '@system.router'

export default {
  data: {
    imageList: [
      {
        src: 'http://fishing-h5.springdance.cn/images/wallpaper/1.webp',
      },
      {
        src: 'http://fishing-h5.springdance.cn/images/wallpaper/11.webp',
      },
      {
        src: 'http://fishing-h5.springdance.cn/images/wallpaper/5.webp',
      },
      {
        src: 'http://fishing-h5.springdance.cn/images/wallpaper/10.webp',
      },
      {
        src: 'http://fishing-h5.springdance.cn/images/wallpaper/9.webp',
      },
      {
        src: 'http://fishing-h5.springdance.cn/images/wallpaper/4.webp',
      },
      {
        src: 'http://fishing-h5.springdance.cn/images/wallpaper/7.webp',
      },
      {
        src: 'http://fishing-h5.springdance.cn/images/wallpaper/3.webp',
      },
      {
        src: 'http://fishing-h5.springdance.cn/images/wallpaper/12.webp',
      },
      {
        src: 'http://fishing-h5.springdance.cn/images/wallpaper/6.webp',
      },
      {
        src: 'http://fishing-h5.springdance.cn/images/wallpaper/8.webp',
      },
      {
        src: 'http://fishing-h5.springdance.cn/images/wallpaper/2.webp',
      },
    ],
  },
  goToDetail: function (src) {
    router.push({
      uri: 'wallpaper/detail',
      params: {
        image: src,
      },
    })
  },
}
</script>

<style>
.page {
  flex-direction: column;
  background-color: #fb6767;
  height: 100%;
}

.title {
  margin-top: 106px;
  margin-bottom: 36px;
  margin-left: 40px;
  font-size: 48px;
  color: white;
}

.grid {
  columns: 2;
  column-gap: 20px;
  flex-grow: 1;
}

.grid-item {
  flex-direction: column;
  padding-left: 8px;
  padding-right: 8px;
  margin-bottom: 15px;
}

.wallpaper-image {
  width: 100%;
  height: 470px;
  border-radius: 20px;
}
</style>
