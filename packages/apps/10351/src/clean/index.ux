<template>
  <div class="wrapper">
    <image src="assets/clean-logo.png"></image>
    <text class="big">发现 {{ num }}GB 垃圾文件</text>
    <div class="clean-bg"></div>
    <stack class="clean-button" @click="onButtonClick">
      <image src="assets/clean-circle.png"></image>
      <div class="clean-text">
        <text>开始清理</text>
      </div>
    </stack>
  </div>
</template>

<script>
import router from '@system.router'

export default {
  props: {
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data: {
    num: 0,
  },

  onInit() {
    this.getInitData()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  onReady() {},

  onDestroy() {},

  handleStorageChange() {
    this.getInitData()
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      this.getInitData()
    }
  },
  async getInitData() {
    this.num = (Math.random() * 5).toFixed(2)
  },
  onButtonClick() {
    router.push({
      uri: 'clean/cleaning',
      params: {
        num: this.num,
      },
    })
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #444ce4;

  image {
    width: 170px;
    height: 200px;
  }

  .big {
    font-size: 40px;
    color: #fff;
    font-weight: bold;
    margin-top: 20px;
  }

  .clean-bg {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 230px;
    background-color: white;
    border-top-left-radius: 60px;
    border-top-right-radius: 60px;
  }

  .clean-button {
    position: absolute;
    left: 145px;
    bottom: 0;
    width: 460px;
    height: 460px;

    image {
      width: 460px;
      height: 460px;
    }

    .clean-text {
      width: 460px;
      height: 460px;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      text {
        font-size: 36px;
        color: #3d90ff;
        font-weight: bold;
        margin-bottom: 50px;
      }
    }
  }
}
</style>
