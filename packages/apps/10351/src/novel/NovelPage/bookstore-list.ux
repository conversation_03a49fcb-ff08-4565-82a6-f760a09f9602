<template>
  <div class="content">
    <div class="hot-header">
      <!--<image class="left-icon" src="/assets/images/ic_hander.webp"></image>-->
      <text class="title">小编推荐</text>
      <!-- <image src="/assets/images/ic_start.png" class="start"></image> -->
      <div class="right" @click="handleSeeMore">
        <text class="more">更多</text>
        <image
          class="icon"
          src="/assets/images/ic_arrow_right_white.webp"
        ></image>
      </div>
    </div>
    <list class="content-header">
      <list-item
        type="item"
        class="type-item {{currentIndex === index ? 'type-item--active' : ''}}"
        for="(index, item) in categoryList"
        tid="{{index}}"
      >
        <!--<div show="{{currentIndex === index}}" class="active-bg"></div>-->
        <div>
          <text
            class="text {{currentIndex === index ? 'active' : ''}}"
            @click="handleChangeTab(item, index)"
          >
            {{ item.category }}
          </text>
        </div>
      </list-item>
    </list>
    <div class="content-list">
      <div class="item" for="item in list" tid="$idx" @click="toRead(item)">
        <div class="item__content">
          <image class="item__content__cover" src="{{item.cover}}"></image>
          <div class="item__content__info">
            <text class="item__content__info__title">{{ item.name }}</text>
            <!-- <text class="item__footer__type">{{ currentType.category }}</text> -->
            <text class="item__footer__chapter">
              更新至{{ item.chapterCount }}章
            </text>
            <!-- <text class="item__content__info__introduction">
              {{ item.introduction }}
            </text> -->
          </div>
        </div>
        <!-- <div class="item__footer">
          <text class="item__footer__type">{{ currentType.category }}</text>
          <text class="item__footer__chapter">
            更新至{{ item.chapterCount }}章
          </text>
        </div> -->
      </div>
      <div class="item"></div>
      <div class="item"></div>
      <div class="item"></div>
      <div class="item"></div>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { trackEvent } from '@quickapp/business'

export default {
  data: {
    currentIndex: 0,
  },

  props: {
    defaultIndex: {
      type: Number,
    },
    categoryList: {
      type: Array,
      default: () =>
        new Array(4).fill({
          id: 2,
          category: '都市',
          bookList: new Array(10).fill({
            cover:
              'http://img.1391.com/api/v1/bookcenter/cover/1/3516087/3516087_37d6592bfb0747c4b99768183cdd8bb2.jpg',
            id: 6,
            introduction:
              '一觉醒来，萧君临穿越进了自己看的小说里，可他不是主角，而是男配他爸，这男配还是个舔狗。“老子一世英名都让你毁了，今日起解除我儿子在集团的一切职务。”「叮，宿主与儿子断绝关系，获得年轻二十岁奖励。」“别叫我伯父，臭丫头只知道利用我儿子，今天开始你降职去楼下扫厕所。”「叮，宿主成功打压女主，获得神级投资技能、神级格斗术。」杀舔狗、压女主、夺主角造化，萧君临发现，自己正逐渐走向人生巅峰。',
            name: '穿越成舔狗他爸，我才是天命主角',
          }),
        }),
    },
  },

  computed: {
    currentType() {
      return this.typeList[this.currentIndex]
    },
    typeList() {
      return this.categoryList.map(it => ({
        id: it.id,
        category: it.category,
      }))
    },
    list() {
      const res = this.categoryList[this.currentIndex]
      return res && res.bookList ? res.bookList : []
    },
  },

  onInit() {
    this.currentIndex = Number(this.defaultIndex || 0)
    this.tabTrackEvt()
  },

  handleChangeTab(item, index) {
    this.currentIndex = index
    this.$emit('changeTab', {
      type: item,
      index: this.currentIndex,
    })

    this.tabTrackEvt()
  },

  tabTrackEvt(opt_value = this.currentIndex + 1) {
    trackEvent({
      category: 'page',
      action: 'click',
      opt_label: 'book_town_classify_click',
      opt_value: opt_value,
    })
  },

  handleSeeMore() {
    this.$emit('seeMore', this.currentType)
  },

  toRead(item) {
    router.push({
      uri: 'novel/introduce',
      params: {
        id: item.id,
      },
    })
  },
}
</script>

<style lang="less">
.hot-header {
  align-items: center;
  margin-bottom: 34px;
  /* position: relative; */
  .start {
    position: relative;
    top: -8px;
  }
  .left-icon {
    width: 44px;
    height: 44px;
    margin-right: 16px;
  }

  .title {
    font-size: 36px;
    font-weight: bolder;
    color: #333;
  }

  .right {
    margin-left: auto;
  }

  .more {
    font-size: 28px;
    color: #333;
  }

  .icon {
    width: 40px;
    height: 40px;
  }
}

.content {
  width: 100%;
  flex-direction: column;
  margin-top: 60px;
}

.content-header {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  height: 80px;
  width: 100%;

  .type-item {
    height: 60px;
    padding: 0 32px;
    margin-left: 12px;
    /* margin-right: 32px; */
    margin-bottom: 20px;
    border-radius: 26px;
    /* background-color: #e1e7ee; */

    &--active {
      background-color: rgba(232, 138, 173, 0.2);
      /* border-bottom: 4px solid #b87659; */
    }

    .text {
      width: 100%;
      text-align: center;
      border-radius: 12px;
      font-size: 28px;
      font-weight: 500;
      color: #000;
    }

    .active {
      color: #926fff;
    }
  }
}

.content-list {
  flex-direction: row;
  justify-content: space-around;
  align-items: flex-start;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 20px;

  .item {
    flex-direction: column;
    width: 31%;
    margin-bottom: 40px;
    background-color: white;
    border-radius: 32px;

    &__content {
      flex-direction: column;
      padding-bottom: 10px;
      width: 100%;
      /* height: 252px; */
      &__cover {
        width: 100%;
        height: 280px;
        border-radius: 24px;
      }

      &__info {
        /* margin-left: 24px; */
        flex-direction: column;
        padding: 10px;

        &__title {
          font-size: 28px;
          lines: 1;
          text-overflow: ellipsis;
          font-weight: bolder;
          color: #000;
        }

        &__introduction {
          font-size: 28px;
          color: #333;
          line-height: 36px;
          lines: 2;
          margin-top: auto;
          text-overflow: ellipsis;
        }
      }
    }

    &__footer {
      /* height: 74px; */
      /* padding: 0 24px; */
      /* background-color: #ebf7f6; */
      /* border-bottom-left-radius: 32px; */
      /* border-bottom-right-radius: 32px; */
      margin-top: auto;

      &__type {
        font-size: 28px;
        margin-top: 10px;
        /* font-weight: bolder; */
        /* color: #ff8b20; */
      }

      &__chapter {
        font-size: 28px;
        margin-bottom: 10px;
        text-align: center;
        /* color: #33b4a9; */
        /* margin-left: 18px; */
      }
    }
  }
}

.see-more {
  position: fixed;
  right: 20px;
  bottom: 150px;
  padding: 15px;
  height: 70px;
  width: 240px;
  border-radius: 20px;
  background-color: #ffff00;
  font-size: 24px;
}
</style>
