/**
 * 书城
 */
import { request } from '@quickapp/business/lib/config'

export function getBookstoreData() {
  return request({
    method: 'GET',
    url: '/novel/bookStore',
  })
}

/**
 * 分类下的书籍
 * @returns {Promise | Promise<unknown>}
 */
export function getCategoryData({ id, pageNum, size }) {
  return request({
    method: 'GET',
    url: '/novel/category',
    data: { id, pageNum, size },
  })
}
