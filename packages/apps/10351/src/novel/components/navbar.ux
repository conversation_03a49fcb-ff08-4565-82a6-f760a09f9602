<template>
  <div class="wrapper">
    <div if="showStatusBar" style="{{statusBarStyle}}"></div>
    <div class="inner">
      <image
        @click="toBack"
        class="ic-back"
        src="/assets/images/ic_back.webp"
      ></image>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
export default {
  data: {
    title: 'navbar',
  },

  props: {
    showStatusBar: {
      type: Boolean,
    },
  },

  computed: {
    statusBarStyle() {
      return {
        height: this.$page.statusBarHeight + 'px',
      }
    },
  },

  onInit() {},

  toBack() {
    router.back()
  },
}
</script>

<style lang="less">
.wrapper {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  flex-direction: column;
}

.placeholder {
  height: 88px;
}

.inner {
  height: 88px;
  padding: 0 30px;
  align-items: center;

  .ic-back {
    width: 60px;
    height: 60px;
  }
}
.title {
  text-align: center;
  color: #212121;
}
</style>
