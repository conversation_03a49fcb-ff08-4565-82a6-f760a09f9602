<template>
  <div class="root">
    <div class="date">
      <text>{{ date }}</text>
    </div>
    <div class="content">
      <div class="left">
        <text class="title">
          {{ thing }}
        </text>
        <text class="time">
          {{ time }}
        </text>
      </div>
      <div class="right">
        <text class="{{ symbol==='-' ? 'disburse' : 'income' }}">{{ symbol }}</text>
        <text class="amount {{ symbol==='-' ? 'disburse' : 'income' }}">
          {{ amount }}
        </text>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    date: {
      type: String,
    },
    amount: {
      type: String,
    },
    thing: {
      type: String,
    },
    time: {
      type: String,
    },
    symbol: {
      type: String,
    }
  },
}
</script>

<style lang="less">
.root {
  background-color: #fff;
  border-radius: 10px;
  padding: 10px;
  margin-bottom: 15px;
  flex-direction: column;
  .date {
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #aaaaaa;
  }
  .content {
    height: 100px;
    width: 100%;
    display: flex;
    .left {
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-right: 12px;
      justify-content: center;
      .title {
        text-overflow: ellipsis;
        lines: 1;
        font-size: 36px;
        color: #000;
      }
      .time {
        font-size: 22px;
      }
    }
    .right {
      width: 25%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .disburse {
    color: #c8102e;
  }
  .income {
    color: #000;
  }
}
</style>
