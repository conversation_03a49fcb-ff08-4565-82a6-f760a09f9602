<script>
export default {
  data() {
    return {
      image: '',
      name: '',
      desc: '',
    }
  },
  onReady() {
    console.log('foodItem', this.name)
  },
}
</script>

<template>
  <div class="wrapper">
    <image src="{{image}}"></image>
    <text class="name">{{ name }}</text>
    <text class="desc">{{ desc }}</text>
  </div>
</template>

<style scoped lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  overflow: auto;
  flex-direction: column;
  align-items: center;
  image {
    width: 600px;
    height: 600px;
    margin: 40px 0;
  }

  .name {
    font-size: 64px;
    font-weight: bold;
    color: #454545;
  }

  .desc {
    font-size: 40px;
    color: #454545;
    margin: 60px 56px;
  }
}
</style>
