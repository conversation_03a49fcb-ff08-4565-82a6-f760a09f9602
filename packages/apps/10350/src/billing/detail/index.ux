<template>
  <div class="wrapper">
    <list class="content-header">
      <list-item
        type="item"
        class="type-item {{currentIndex === index ? 'type-item--active' : ''}}"
        for="(index, item) in categoryList"
        tid="{{index}}"
      >
        <div>
          <text
            class="text {{currentIndex === index ? 'active' : ''}}"
            @click="handleChangeTab(item, index)"
          >
            {{ item.name }}
          </text>
        </div>
      </list-item>
    </list>
    <div class="input">
      <text>缘由:</text>
      <input type="text" value="{{thing}}" @change="handleThingChange" />
    </div>
    <div class="input">
      <text>金额:</text>
      <input type="text" value="{{amount}}" @change="handleAmountChange" />
    </div>
    <input type="button" value="确定" class="btn" @click="saveOptions" />
  </div>
</template>

<script>
import router from '@system.router'
import { showToast, storage } from '@quickapp/utils'
import dayjs from 'dayjs'

export default {
  data: {
    currentIndex: 0,
    date: "",
    amount: "",
    thing: "",
    time: "",
    symbol: "-",
    billingData: [],
    categoryList: [
      {
        name: "支出",
        value: "-"
      },
      {
        name: "收入",
        value: "+"
      }
    ]
  },
  onInit() {
    this.getLocalData()
  },
  back() {
    router.back()
  },
  handleThingChange(e) {
    this.thing = e.value

  },
  handleAmountChange(e) {
    this.amount = e.value
  },


  async getLocalData() {
    await storage.get('billingData').then(res => {
      console.log("account_res", res);
      this.billingData = res || []
    })
  },
  // 保存到本地
  async saveOptions() {
    this.date = dayjs(new Date()).format('YYYY-MM-DD')
    this.time = dayjs(new Date()).format('HH:mm')
    const billingData = {
      thing: this.thing,
      amount: Number(this.amount).toFixed(2),
      symbol: this.symbol,
      date: this.date,
      time: this.time
    }
    this.billingData.unshift(billingData)
    const saveBillingData = this.billingData.filter((item) =>
      !isNaN(item.amount) && item.thing
    )
    await storage.set('billingData', saveBillingData)
    showToast('保存成功')
    this.thing = ""
    this.amount = ""
    router.back()
  },

  handleChangeTab(item, index) {
    this.currentIndex = index
    this.symbol = item.value
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 40px;
  background-color: #ffffff;
  .input {
    border-radius: 10px;
    border: 1px solid #979797;
    width: 100%;
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px 20px;
    display: flex;
    text {
      font-size: 36px;
    }
    input {
      flex: 1;
    }
  }
  .content-header {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    height: 80px;
    width: 100%;
    .type-item {
      height: 60px;
      padding: 0 32px;
      margin-left: 12px;
      margin-bottom: 20px;
      border-radius: 26px;

      &--active {
        background-color: #ebefff;
      }

      .text {
        width: 100%;
        border-radius: 12px;
        font-size: 36px;
        font-weight: 500;
        color: #000;
      }

      .active {
        color: #3860ff;
      }
    }
  }
  .btn {
    width: 100%;
    border: 1px solid #979797;
    border-radius: 20px;
    background-color: #e9e9e9;
    padding: 10px;
    color: #424242;
    margin-top: 20px;
  }
}
</style>
